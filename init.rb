# Redmine Document Viewer Plugin
# 在线文档查看器插件

Redmine::Plugin.register :redmine_document_viewer do
  name 'Redmine Document Viewer Plugin'
  author 'dungang'
  description '支持在线预览Word、Excel、PDF、图片等文档附件'
  version '1.0.0'
  url 'https://gitee.com/dungang/redmine_document_viewer'
  author_url 'https://gitee.com/dungang'

  # 全局权限设置
  permission :view_document_preview, {
    :document_viewer => [:preview, :show]
  }, :public => true

  # 设置页面
  settings :default => {
    'enable_word_preview' => true,
    'enable_excel_preview' => true,
    'enable_pdf_preview' => true,
    'enable_image_preview' => true,
    'max_file_size' => 50, # MB
    'preview_method' => 'vue_office' # vue_office, kkfileview, onlyoffice
  }, :partial => 'settings/document_viewer_settings'
end

# 扩展附件模型，添加预览功能
require_dependency 'attachment'
class Attachment
  def previewable?
    return false unless readable?

    settings = Setting.plugin_redmine_document_viewer || {}

    case filename.downcase
    when /\.(jpg|jpeg|png|gif|bmp|webp)$/i
      settings['enable_image_preview'] == true
    when /\.(pdf)$/i
      settings['enable_pdf_preview'] == true
    when /\.(docx?|rtf)$/i
      settings['enable_word_preview'] == true
    when /\.(xlsx?|csv)$/i
      settings['enable_excel_preview'] == true
    when /\.(pptx?|ppt)$/i
      settings['enable_powerpoint_preview'] == true
    else
      false
    end
  end

  def preview_url
    Rails.application.routes.url_helpers.doc_preview_path(self)
  end
end

# 加载钩子
require File.expand_path('../lib/document_viewer_hooks', __FILE__)
