<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>文档预览插件测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .attachment-link { color: #169; text-decoration: none; }
        .attachment-link:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <h1>Redmine文档预览插件测试页面</h1>
    
    <div class="test-section">
        <h3>测试附件链接（应该会自动添加预览按钮）</h3>
        
        <p>
            <a href="/attachments/download/123/报告.docx" class="icon-attachment attachment-link">报告.docx</a>
            <span class="size">(2.3 MB)</span>
        </p>
        
        <p>
            <a href="/attachments/download/124/数据表.xlsx" class="icon-attachment attachment-link">数据表.xlsx</a>
            <span class="size">(1.8 MB)</span>
        </p>
        
        <p>
            <a href="/attachments/download/125/演示文稿.pptx" class="icon-attachment attachment-link">演示文稿.pptx</a>
            <span class="size">(5.2 MB)</span>
        </p>
        
        <p>
            <a href="/attachments/download/126/截图.png" class="icon-attachment attachment-link">截图.png</a>
            <span class="size">(856 KB)</span>
        </p>
        
        <p>
            <a href="/attachments/download/127/文档.pdf" class="icon-attachment attachment-link">文档.pdf</a>
            <span class="size">(3.1 MB)</span>
        </p>
        
        <p>
            <a href="/attachments/download/128/压缩包.zip" class="icon-attachment attachment-link">压缩包.zip</a>
            <span class="size">(10.5 MB)</span>
            <em>（此文件不支持预览，不应显示预览按钮）</em>
        </p>
    </div>
    
    <div class="test-section">
        <h3>表格中的附件</h3>
        <table class="list files">
            <thead>
                <tr>
                    <th>文件名</th>
                    <th>大小</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><a href="/attachments/download/129/合同.docx" class="attachment-link">合同.docx</a></td>
                    <td>1.2 MB</td>
                    <td><!-- 预览按钮应该添加到这里 --></td>
                </tr>
                <tr>
                    <td><a href="/attachments/download/130/财务报表.xlsx" class="attachment-link">财务报表.xlsx</a></td>
                    <td>2.8 MB</td>
                    <td><!-- 预览按钮应该添加到这里 --></td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div class="test-section">
        <h3>插件状态检查</h3>
        <div id="plugin-status">
            <p>正在检查插件状态...</p>
        </div>
    </div>

    <!-- 加载文档查看器JavaScript -->
    <script src="assets/javascripts/document_viewer.js"></script>
    
    <script>
        // 检查插件是否正常加载
        setTimeout(function() {
            const statusDiv = document.getElementById('plugin-status');
            const previewButtons = document.querySelectorAll('.document-preview-btn');
            
            if (previewButtons.length > 0) {
                statusDiv.innerHTML = `
                    <p style="color: green;">✅ 插件正常工作！</p>
                    <p>检测到 ${previewButtons.length} 个预览按钮</p>
                `;
            } else {
                statusDiv.innerHTML = `
                    <p style="color: red;">❌ 插件未正常工作</p>
                    <p>未检测到预览按钮，请检查：</p>
                    <ul>
                        <li>JavaScript文件是否正确加载</li>
                        <li>浏览器控制台是否有错误</li>
                        <li>文件路径是否正确</li>
                    </ul>
                `;
            }
        }, 1000);
        
        // 手动触发预览按钮添加（用于测试）
        function testAddPreviewButtons() {
            if (typeof addPreviewButtons === 'function') {
                addPreviewButtons();
                console.log('手动触发预览按钮添加');
            } else {
                console.error('addPreviewButtons函数未找到');
            }
        }
        
        // 添加测试按钮
        const testButton = document.createElement('button');
        testButton.textContent = '手动添加预览按钮';
        testButton.onclick = testAddPreviewButtons;
        testButton.style.margin = '10px 0';
        document.querySelector('.test-section:last-child').appendChild(testButton);
    </script>
</body>
</html>
