#!/bin/bash

# Redmine文档查看器插件快速安装脚本

set -e

echo "🚀 Redmine文档查看器插件 - 快速安装脚本"
echo "================================================"
echo

# 检查Node.js是否安装
check_nodejs() {
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js未安装，请先安装Node.js (版本 >= 16.0.0)"
        echo "   下载地址: https://nodejs.org/"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2)
    REQUIRED_VERSION="16.0.0"
    
    if ! node -e "process.exit(require('semver').gte('$NODE_VERSION', '$REQUIRED_VERSION'))" 2>/dev/null; then
        echo "❌ Node.js版本过低 (当前: $NODE_VERSION, 需要: >= $REQUIRED_VERSION)"
        exit 1
    fi
    
    echo "✅ Node.js版本检查通过 (v$NODE_VERSION)"
}

# 检查npm是否可用
check_npm() {
    if ! command -v npm &> /dev/null; then
        echo "❌ npm未安装"
        exit 1
    fi
    echo "✅ npm可用"
}

# 安装前端依赖
install_dependencies() {
    echo
    echo "📦 安装前端依赖..."
    cd web
    
    if [ -f "package-lock.json" ]; then
        npm ci
    else
        npm install
    fi
    
    echo "✅ 依赖安装完成"
    cd ..
}

# 构建前端项目
build_frontend() {
    echo
    echo "🔨 构建前端项目..."
    cd web
    npm run build
    echo "✅ 前端构建完成"
    cd ..
}

# 复制资源文件
copy_assets() {
    echo
    echo "📁 复制资源文件到assets目录..."
    cd web
    npm run copy-assets
    echo "✅ 资源文件复制完成"
    cd ..
}

# 清理旧的资源文件
clean_old_assets() {
    echo
    echo "🧹 清理旧的资源文件..."
    
    # 删除旧的CDN文件
    if [ -d "cdn" ]; then
        rm -rf cdn
        echo "✅ 删除旧的CDN目录"
    fi
    
    # 删除旧的脚本文件
    OLD_SCRIPTS=(
        "download_assets.sh"
        "update_assets.sh" 
        "copy_local_assets.sh"
        "copy_from_cdn.sh"
        "check_assets.sh"
        "test_vue_office.html"
        "test_preview.html"
    )
    
    for script in "${OLD_SCRIPTS[@]}"; do
        if [ -f "$script" ]; then
            rm "$script"
            echo "✅ 删除旧脚本: $script"
        fi
    done
    
    # 删除旧的视图文件
    OLD_VIEWS=(
        "app/views/document_viewer/vue_office_preview.html.erb"
        "app/views/document_viewer/fallback_preview.html.erb"
        "app/views/document_viewer/simple_preview.html.erb"
    )
    
    for view in "${OLD_VIEWS[@]}"; do
        if [ -f "$view" ]; then
            rm "$view"
            echo "✅ 删除旧视图: $view"
        fi
    done
}

# 显示安装结果
show_results() {
    echo
    echo "🎉 安装完成！"
    echo "=============="
    echo
    
    # 检查生成的文件
    if [ -d "assets/javascripts" ]; then
        JS_COUNT=$(ls -1 assets/javascripts/*.js 2>/dev/null | wc -l)
        echo "📄 JavaScript文件: $JS_COUNT 个"
        ls -la assets/javascripts/*.js 2>/dev/null || true
    fi
    
    if [ -d "assets/stylesheets" ]; then
        CSS_COUNT=$(ls -1 assets/stylesheets/*.css 2>/dev/null | wc -l)
        echo "🎨 CSS文件: $CSS_COUNT 个"
        ls -la assets/stylesheets/*.css 2>/dev/null || true
    fi
    
    echo
    echo "📋 下一步操作："
    echo "1. 重启Redmine服务器"
    echo "2. 在Redmine管理界面中配置插件"
    echo "3. 测试文档预览功能"
    echo
    echo "🔧 开发命令："
    echo "   cd web && npm run dev          # 启动开发服务器"
    echo "   cd web && npm run build:watch  # 监听模式（推荐）"
    echo "   cd web && npm run build        # 构建生产版本"
    echo
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  --help, -h     显示此帮助信息"
    echo "  --clean        只清理旧文件，不安装"
    echo "  --build-only   只构建，不安装依赖"
    echo
    echo "示例:"
    echo "  $0             # 完整安装"
    echo "  $0 --clean     # 只清理旧文件"
    echo "  $0 --build-only # 只构建项目"
}

# 主函数
main() {
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --help|-h)
                show_help
                exit 0
                ;;
            --clean)
                clean_old_assets
                echo "✅ 清理完成"
                exit 0
                ;;
            --build-only)
                BUILD_ONLY=true
                shift
                ;;
            *)
                echo "❌ 未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查环境
    check_nodejs
    check_npm
    
    # 清理旧文件
    clean_old_assets
    
    # 安装和构建
    if [ "$BUILD_ONLY" != "true" ]; then
        install_dependencies
    fi
    
    build_frontend
    copy_assets
    
    # 显示结果
    show_results
}

# 错误处理
trap 'echo "❌ 安装过程中出现错误"; exit 1' ERR

# 运行主函数
main "$@"
