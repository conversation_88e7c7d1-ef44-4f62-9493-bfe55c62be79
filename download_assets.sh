#!/bin/bash

# 下载vue-office本地资源文件脚本
# 用于局域网环境下的文档预览功能

echo "开始下载vue-office本地资源文件..."

# 创建assets目录
mkdir -p assets/javascripts
mkdir -p assets/stylesheets

# 下载Vue.js
echo "下载Vue.js..."
curl -L -o assets/javascripts/vue.global.js https://unpkg.com/vue@3.5.15/dist/vue.global.js

# 下载vue-office组件
echo "下载vue-office-docx..."
curl -L -o assets/javascripts/vue-office-docx.umd.js https://unpkg.com/@vue-office/docx@1.6.2/lib/index.umd.js
curl -L -o assets/stylesheets/vue-office-docx.css https://unpkg.com/@vue-office/docx@1.6.2/lib/index.css

echo "下载vue-office-excel..."
curl -L -o assets/javascripts/vue-office-excel.umd.js https://unpkg.com/@vue-office/excel@1.7.11/lib/index.umd.js
curl -L -o assets/stylesheets/vue-office-excel.css https://unpkg.com/@vue-office/excel@1.7.11/lib/index.css

echo "下载vue-office-pdf..."
curl -L -o assets/javascripts/vue-office-pdf.umd.js https://unpkg.com/@vue-office/pdf@2.0.2/lib/index.umd.js
curl -L -o assets/stylesheets/vue-office-pdf.css https://unpkg.com/@vue-office/pdf@2.0.2/lib/index.css

echo "所有文件下载完成！"

# 检查文件大小
echo "检查下载的文件："
ls -lh assets/javascripts/
ls -lh assets/stylesheets/

echo "如果文件大小为0或很小，请检查网络连接并重新运行此脚本。"
