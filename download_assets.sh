#!/bin/bash

# 下载vue-office本地资源文件脚本
# 用于局域网环境下的文档预览功能

echo "开始下载vue-office本地资源文件..."

# 创建assets目录
mkdir -p assets/javascripts
mkdir -p assets/stylesheets

# 下载Vue.js (使用jsdelivr镜像)
echo "下载Vue.js..."
curl -L -o assets/javascripts/vue.global.js https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.js

# 下载vue-office组件 (使用jsdelivr镜像)
echo "下载vue-office-docx..."
curl -L -o assets/javascripts/vue-office-docx.umd.js https://cdn.jsdelivr.net/npm/@vue-office/docx@1.6.2/lib/index.umd.js
curl -L -o assets/stylesheets/vue-office-docx.css https://cdn.jsdelivr.net/npm/@vue-office/docx@1.6.2/lib/index.css

echo "下载vue-office-excel..."
curl -L -o assets/javascripts/vue-office-excel.umd.js https://cdn.jsdelivr.net/npm/@vue-office/excel@1.7.11/lib/index.umd.js
curl -L -o assets/stylesheets/vue-office-excel.css https://cdn.jsdelivr.net/npm/@vue-office/excel@1.7.11/lib/index.css

echo "下载vue-office-pdf..."
curl -L -o assets/javascripts/vue-office-pdf.umd.js https://cdn.jsdelivr.net/npm/@vue-office/pdf@2.0.2/lib/index.umd.js
curl -L -o assets/stylesheets/vue-office-pdf.css https://cdn.jsdelivr.net/npm/@vue-office/pdf@2.0.2/lib/index.css

echo "所有文件下载完成！"

# 检查文件大小
echo "检查下载的文件："
ls -lh assets/javascripts/
ls -lh assets/stylesheets/

# 检查是否下载成功
vue_size=$(stat -f%z assets/javascripts/vue.global.js 2>/dev/null || echo 0)
if [ "$vue_size" -lt 1000 ]; then
    echo "Vue.js下载失败，尝试备用源..."
    curl -L -o assets/javascripts/vue.global.js https://unpkg.com/vue@3.5.15/dist/vue.global.js
fi

echo "如果文件大小仍然很小，请手动下载以下文件："
echo "1. Vue.js: https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.js"
echo "2. vue-office-docx: https://cdn.jsdelivr.net/npm/@vue-office/docx@1.6.2/lib/index.umd.js"
echo "3. vue-office-excel: https://cdn.jsdelivr.net/npm/@vue-office/excel@1.7.11/lib/index.umd.js"
echo "4. vue-office-pdf: https://cdn.jsdelivr.net/npm/@vue-office/pdf@2.0.2/lib/index.umd.js"
