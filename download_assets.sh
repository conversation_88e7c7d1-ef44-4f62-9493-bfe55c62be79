#!/bin/bash

# 下载vue-office本地资源文件脚本
# 用于局域网环境下的文档预览功能

echo "开始下载vue-office本地资源文件..."

# 创建assets目录
mkdir -p assets/javascripts
mkdir -p assets/stylesheets

# 下载Vue.js (使用jsdelivr镜像)
echo "下载Vue.js..."
curl -L -o assets/javascripts/vue.global.js https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.js

# 下载vue-office组件 (使用正确的URL和版本)
echo "下载vue-office-docx..."
curl -L -o assets/javascripts/vue-office-docx.min.js https://cdn.jsdelivr.net/npm/@vue-office/docx@1.6.3/lib/v3/index.min.js
curl -L -o assets/stylesheets/vue-office-docx.min.css https://cdn.jsdelivr.net/npm/@vue-office/docx@1.6.3/lib/v3/index.min.css

echo "下载vue-office-excel..."
curl -L -o assets/javascripts/vue-office-excel.umd.min.js https://cdn.jsdelivr.net/npm/@vue-office/excel@1.7.14/lib/v2/vue-office-excel.umd.min.js
curl -L -o assets/stylesheets/vue-office-excel.min.css https://cdn.jsdelivr.net/npm/@vue-office/excel@1.7.14/lib/v3/index.min.css

echo "下载vue-office-pdf..."
curl -L -o assets/javascripts/vue-office-pdf.umd.min.js https://cdn.jsdelivr.net/npm/@vue-office/pdf@2.0.10/lib/v3/vue-office-pdf.umd.min.js

echo "所有文件下载完成！"

# 检查文件大小
echo "检查下载的文件："
ls -lh assets/javascripts/
ls -lh assets/stylesheets/

echo "如果文件大小仍然很小，请手动下载以下文件："
echo "1. Vue.js: https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.js"
echo "2. vue-office-docx: https://cdn.jsdelivr.net/npm/@vue-office/docx@1.6.2/lib/index.umd.js"
echo "3. vue-office-excel: https://cdn.jsdelivr.net/npm/@vue-office/excel@1.7.11/lib/index.umd.js"
echo "4. vue-office-pdf: https://cdn.jsdelivr.net/npm/@vue-office/pdf@2.0.2/lib/index.umd.js"
