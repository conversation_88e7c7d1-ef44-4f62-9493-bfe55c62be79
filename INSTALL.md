# Redmine Document Viewer Plugin 安装指南

## 快速安装

### 1. 下载插件
```bash
cd /path/to/redmine/plugins
# 复制插件文件到正确位置
cp -r /path/to/source/redmine_document_viewer ./
```

### 2. 检查文件权限
```bash
# 确保Redmine用户有读取权限
chown -R redmine:redmine /path/to/redmine/plugins/redmine_document_viewer
chmod -R 755 /path/to/redmine/plugins/redmine_document_viewer
```

### 3. 重启Redmine
```bash
# 重启你的Web服务器
sudo systemctl restart apache2
# 或
sudo systemctl restart nginx
# 或使用Passenger
touch /path/to/redmine/tmp/restart.txt
```

### 3. 配置插件
1. 以管理员身份登录Redmine
2. 进入 **管理** → **插件**
3. 找到 **Redmine Document Viewer Plugin**
4. 点击 **配置**
5. 启用需要的文件类型预览功能

## 使用方法

安装完成后，您会在以下位置看到**预览**按钮：

### 1. 问题页面
- 在问题的附件列表中，每个支持预览的文件旁边会出现蓝色的**预览**按钮
- 点击预览按钮会在新窗口中打开文档预览

### 2. 文档页面
- 在项目文档的附件列表中会显示预览按钮

### 3. Wiki页面
- 在Wiki页面的附件中会显示预览按钮

### 4. 其他附件位置
- 任何显示附件的地方都会自动添加预览按钮

## 支持的文件格式

- **图片**: JPG, JPEG, PNG, GIF, BMP, WebP
- **PDF文档**: PDF
- **Word文档**: DOCX, DOC, RTF
- **Excel表格**: XLSX, XLS, CSV
- **PowerPoint演示**: PPTX, PPT

## 预览方式配置

### Vue Office (默认，推荐)
- 纯前端解决方案，无需额外服务
- 支持大部分常用Office格式
- 加载速度快，用户体验好

### kkFileView (可选)
如果需要更多格式支持，可以配置kkFileView：

1. 部署kkFileView服务：
```bash
# 下载kkFileView
wget https://github.com/kekingcn/kkFileView/releases/download/v4.1.0/kkFileView-4.1.0.tar.gz
tar -xzf kkFileView-4.1.0.tar.gz
cd kkFileView-4.1.0/bin
./startup.sh
```

2. 在插件配置中：
   - 选择预览方法为 "kkFileView"
   - 设置kkFileView服务地址：`http://localhost:8012`

### OnlyOffice (可选)
如果需要在线编辑功能：

1. 部署OnlyOffice Document Server：
```bash
# 使用Docker部署
docker run -i -t -d -p 8080:80 onlyoffice/documentserver
```

2. 在插件配置中：
   - 选择预览方法为 "OnlyOffice"
   - 设置OnlyOffice服务地址：`http://localhost:8080`

## 故障排除

### 问题1: 看不到预览按钮
**解决方案**：
1. 确认插件已正确安装在 `plugins/redmine_document_viewer` 目录
2. 重启Web服务器
3. 清除浏览器缓存
4. 检查文件格式是否在支持列表中

### 问题2: 预览按钮点击无反应
**解决方案**：
1. 检查浏览器控制台是否有JavaScript错误
2. 确认文件大小是否超过限制（默认50MB）
3. 检查用户是否有查看附件的权限

### 问题3: 预览页面显示空白
**解决方案**：
1. 检查网络连接
2. 确认vue-office库是否正确加载
3. 尝试切换预览方法

### 问题4: 某些文件无法预览
**解决方案**：
1. 确认文件格式在支持列表中
2. 检查文件是否损坏
3. 尝试重新上传文件

## 卸载插件

如果需要卸载插件：

```bash
# 删除插件目录
rm -rf /path/to/redmine/plugins/redmine_document_viewer

# 重启Web服务器
sudo systemctl restart apache2
```

## 技术支持

如果遇到问题，请：

1. 查看Redmine日志：`tail -f log/production.log`
2. 检查浏览器控制台错误
3. 确认插件配置是否正确
4. 提交Issue到项目仓库

## 更新插件

```bash
cd /path/to/redmine/plugins/redmine_document_viewer
git pull origin main
# 重启Web服务器
sudo systemctl restart apache2
```
