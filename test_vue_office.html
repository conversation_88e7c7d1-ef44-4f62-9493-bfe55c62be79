<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Vue Office 组件测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <h1>Vue Office 组件加载测试</h1>
    
    <div class="test-section">
        <h3>组件加载状态</h3>
        <div id="loading-status">正在检查...</div>
    </div>
    
    <div class="test-section">
        <h3>Vue应用测试</h3>
        <div id="vue-app">
            <p v-if="loading">正在加载Vue应用...</p>
            <p v-else class="success">✅ Vue应用加载成功！</p>
            <p>当前时间: {{ currentTime }}</p>
        </div>
    </div>

    <!-- 加载本地Vue和vue-office文件 -->
    <script src="assets/javascripts/vue.global.js"></script>
    
    <!-- 确保Vue全局可用 -->
    <script>
        if (typeof window.Vue === 'undefined' && typeof Vue !== 'undefined') {
            window.Vue = Vue;
        }
    </script>
    
    <script src="assets/javascripts/vue-office-docx.min.js"></script>
    <script src="assets/javascripts/vue-office-excel.umd.min.js"></script>
    <script src="assets/javascripts/vue-office-pdf.umd.min.js"></script>
    
    <script>
        // 检查组件加载状态
        function checkLoadingStatus() {
            const statusDiv = document.getElementById('loading-status');
            let status = [];
            
            // 检查Vue
            if (typeof Vue !== 'undefined') {
                status.push('<span class="success">✅ Vue.js 已加载</span>');
            } else {
                status.push('<span class="error">❌ Vue.js 未加载</span>');
            }
            
            // 检查vue-office组件
            if (typeof VueOfficeDocx !== 'undefined') {
                status.push('<span class="success">✅ VueOfficeDocx 已加载</span>');
            } else {
                status.push('<span class="error">❌ VueOfficeDocx 未加载</span>');
            }
            
            if (typeof VueOfficeExcel !== 'undefined') {
                status.push('<span class="success">✅ VueOfficeExcel 已加载</span>');
            } else {
                status.push('<span class="error">❌ VueOfficeExcel 未加载</span>');
            }
            
            if (typeof VueOfficePdf !== 'undefined') {
                status.push('<span class="success">✅ VueOfficePdf 已加载</span>');
            } else {
                status.push('<span class="error">❌ VueOfficePdf 未加载</span>');
            }
            
            statusDiv.innerHTML = status.join('<br>');
            
            // 如果Vue可用，初始化Vue应用
            if (typeof Vue !== 'undefined') {
                initVueApp();
            }
        }
        
        function initVueApp() {
            try {
                const { createApp } = Vue;
                
                const app = createApp({
                    data() {
                        return {
                            loading: false,
                            currentTime: new Date().toLocaleString()
                        }
                    },
                    
                    mounted() {
                        // 每秒更新时间
                        setInterval(() => {
                            this.currentTime = new Date().toLocaleString();
                        }, 1000);
                    }
                });
                
                app.mount('#vue-app');
                console.log('Vue应用初始化成功');
                
            } catch (error) {
                console.error('Vue应用初始化失败:', error);
                document.getElementById('vue-app').innerHTML = 
                    '<span class="error">❌ Vue应用初始化失败: ' + error.message + '</span>';
            }
        }
        
        // 页面加载完成后检查状态
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', checkLoadingStatus);
        } else {
            checkLoadingStatus();
        }
        
        // 添加一些调试信息
        console.log('=== Vue Office 组件测试 ===');
        console.log('Vue:', typeof Vue);
        console.log('VueOfficeDocx:', typeof VueOfficeDocx);
        console.log('VueOfficeExcel:', typeof VueOfficeExcel);
        console.log('VueOfficePdf:', typeof VueOfficePdf);
        
        // 检查文件是否存在
        function checkFileExists(url) {
            return fetch(url, { method: 'HEAD' })
                .then(response => response.ok)
                .catch(() => false);
        }
        
        // 检查所有资源文件
        const files = [
            'assets/javascripts/vue.global.js',
            'assets/javascripts/vue-office-docx.min.js',
            'assets/javascripts/vue-office-excel.umd.min.js',
            'assets/javascripts/vue-office-pdf.umd.min.js',
            'assets/stylesheets/vue-office-docx.min.css',
            'assets/stylesheets/vue-office-excel.min.css'
        ];
        
        Promise.all(files.map(file => 
            checkFileExists(file).then(exists => ({ file, exists }))
        )).then(results => {
            console.log('=== 文件存在性检查 ===');
            results.forEach(result => {
                console.log(`${result.file}: ${result.exists ? '✅ 存在' : '❌ 不存在'}`);
            });
        });
    </script>
</body>
</html>
