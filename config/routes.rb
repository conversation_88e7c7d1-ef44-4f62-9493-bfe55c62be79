# 路由配置
Rails.application.routes.draw do
  # 全局文档查看器路由
  resources :document_viewer, :only => [:show] do
    member do
      get :preview
      get :download
    end
  end

  # 简化的预览路由
  get 'attachments/:id/preview', :to => 'document_viewer#preview', :as => 'attachment_preview'
  get 'doc_viewer/:id/preview', :to => 'document_viewer#preview', :as => 'doc_preview'
  get 'doc_viewer/:id/download', :to => 'document_viewer#download', :as => 'doc_download'
end
