# 路由配置
Rails.application.routes.draw do
  # 全局文档查看器路由
  resources :document_viewer, :only => [:show] do
    member do
      get :preview
      get :download
    end
  end

  # 简化的预览路由
  get 'attachments/:id/preview', :to => 'document_viewer#preview', :as => 'preview_attachment'
  get 'document_viewer/:id/preview', :to => 'document_viewer#preview', :as => 'preview_document_viewer'
  get 'document_viewer/:id/download', :to => 'document_viewer#download', :as => 'download_document_viewer'
end
