# 🚀 Redmine文档查看器插件 - 新架构说明

## 📋 项目概述

本项目已完全重构，采用现代化的前端技术栈，提供更好的开发体验和用户体验。

### 🎯 核心特点

- **现代化技术栈**: Vite + Vue3 + vue-office
- **组件化开发**: 模块化的Vue组件设计
- **自动化构建**: 一键构建并部署到Redmine
- **智能预览**: 支持多种文档格式的在线预览
- **响应式设计**: 完美适配桌面端和移动端

## 🏗️ 新架构设计

### 前端项目结构

```
web/                           # 独立的前端项目
├── src/
│   ├── components/            # Vue组件
│   │   ├── DocumentViewer.vue        # 主文档查看器
│   │   ├── PreviewApp.vue           # 预览应用主组件
│   │   ├── PreviewHeader.vue        # 预览头部
│   │   └── previews/                # 各格式预览组件
│   │       ├── WordPreview.vue      # Word文档预览
│   │       ├── ExcelPreview.vue     # Excel表格预览
│   │       ├── PdfPreview.vue       # PDF文档预览
│   │       ├── ImagePreview.vue     # 图片预览
│   │       ├── PowerPointPreview.vue # PPT预览
│   │       └── DefaultPreview.vue   # 默认预览
│   ├── styles/                # SCSS样式文件
│   │   ├── variables.scss     # 设计系统变量
│   │   ├── main.scss          # 主样式（Redmine页面）
│   │   └── preview.scss       # 预览页面样式
│   ├── main.js                # 主入口（Redmine页面脚本）
│   └── preview.js             # 预览页面入口
├── scripts/
│   └── copy-assets.js         # 自动复制构建产物
├── package.json               # 项目配置
├── vite.config.js             # Vite构建配置
└── index.html                 # 开发预览页面
```

### Redmine插件结构

```
redmine_document_viewer/       # Redmine插件目录
├── init.rb                    # 插件初始化
├── config/routes.rb           # 路由配置
├── app/
│   ├── controllers/
│   │   └── document_viewer_controller.rb  # 简化的控制器
│   ├── helpers/
│   │   └── document_viewer_helper.rb      # 辅助方法
│   └── views/
│       └── document_viewer/
│           └── preview.html.erb           # 预览页面模板
├── assets/                    # 构建产物目录
│   ├── javascripts/           # 构建后的JS文件
│   │   ├── document-viewer.js # 主脚本
│   │   └── preview-viewer.js  # 预览脚本
│   └── stylesheets/           # 构建后的CSS文件
│       ├── document-viewer.css
│       └── preview-viewer.css
├── lib/
│   └── document_viewer_hooks.rb          # 视图钩子
├── web/                       # 前端项目（开发时）
└── setup.sh                   # 快速安装脚本
```

## 🔄 工作流程

### 1. 开发流程

```bash
# 1. 进入前端项目目录
cd web

# 2. 安装依赖
npm install

# 3. 启动开发服务器
npm run dev

# 4. 在浏览器中查看效果
# http://localhost:3000

# 5. 修改代码，热重载自动更新
```

### 2. 构建和部署

```bash
# 方式一：使用快速安装脚本（推荐）
./setup.sh

# 方式二：手动构建
cd web
npm run build
npm run copy-assets

# 方式三：监听模式（开发时推荐）
cd web
npm run build:watch
```

### 3. 集成测试

```bash
# 1. 构建并复制资源文件
./setup.sh

# 2. 重启Redmine服务器
sudo systemctl restart apache2

# 3. 在Redmine中测试预览功能
```

## 🎨 设计系统

### SCSS变量系统

项目采用完整的设计系统，包含：

- **颜色系统**: 主色、辅助色、状态色
- **间距系统**: xs(4px) → xxl(48px)
- **字体系统**: 大小、权重、行高
- **断点系统**: 响应式设计断点
- **组件变量**: 按钮、卡片等组件样式

### 组件化设计

每个预览组件都是独立的Vue组件：
- 统一的错误处理
- 一致的加载状态
- 响应式设计
- 可访问性支持

## 🚀 技术优势

### 1. 开发体验

- **热重载**: 修改代码立即看到效果
- **组件化**: 模块化开发，易于维护
- **类型安全**: 现代JavaScript特性
- **调试友好**: 完善的错误处理和日志

### 2. 构建优化

- **代码分割**: 按需加载，减少初始包大小
- **资源压缩**: 自动压缩JS和CSS
- **缓存优化**: 合理的文件命名策略
- **兼容性**: 支持现代浏览器

### 3. 用户体验

- **快速加载**: 优化的资源加载策略
- **响应式**: 完美适配各种设备
- **可访问性**: 遵循Web可访问性标准
- **错误处理**: 优雅的错误处理和降级

## 📱 功能特性

### 支持的文件格式

| 格式 | 扩展名 | 预览方式 | 特性 |
|------|--------|----------|------|
| 图片 | jpg, png, gif, webp | 原生预览 | 缩放、全屏、旋转 |
| PDF | pdf | vue-office-pdf | 翻页、缩放、搜索 |
| Word | doc, docx, rtf | vue-office-docx | 完整格式保持 |
| Excel | xls, xlsx, csv | vue-office-excel | 多工作表支持 |
| PowerPoint | ppt, pptx | Office Online | 幻灯片播放 |

### 智能功能

- **自动识别**: 自动识别可预览的文件类型
- **智能降级**: 预览失败时自动降级到下载模式
- **错误恢复**: 完善的错误处理和重试机制
- **性能优化**: 懒加载和缓存策略

## 🔧 配置和定制

### 1. 样式定制

修改 `web/src/styles/variables.scss` 中的变量：

```scss
// 主色调
$primary-color: #3498db;
$primary-hover: #2980b9;

// 预览按钮样式
$preview-btn-bg: $primary-color;
$preview-btn-padding: 4px 12px;
```

### 2. 功能扩展

添加新的预览组件：

1. 在 `web/src/components/previews/` 创建新组件
2. 在 `PreviewApp.vue` 中注册组件
3. 更新文件类型判断逻辑

### 3. 构建配置

修改 `web/vite.config.js` 调整构建行为：

```javascript
// 添加新的入口文件
input: {
  'document-viewer': resolve(__dirname, 'src/main.js'),
  'preview-viewer': resolve(__dirname, 'src/preview.js'),
  'new-feature': resolve(__dirname, 'src/new-feature.js')
}
```

## 🐛 故障排除

### 常见问题

1. **构建失败**
   - 检查Node.js版本 (>= 16.0.0)
   - 删除node_modules重新安装
   - 检查代码语法错误

2. **预览功能异常**
   - 确认资源文件已正确复制
   - 检查浏览器控制台错误
   - 重启Redmine服务器

3. **样式问题**
   - 清除浏览器缓存
   - 确认CSS文件已加载
   - 检查SCSS编译错误

### 调试工具

- **开发服务器**: `npm run dev`
- **构建分析**: 查看构建输出日志
- **浏览器调试**: F12开发者工具
- **网络分析**: 检查资源加载情况

## 🔮 未来规划

- [ ] 支持更多文件格式
- [ ] 添加文档标注功能
- [ ] 支持文档搜索和高亮
- [ ] 添加打印和导出功能
- [ ] 支持文档版本比较
- [ ] 集成AI文档分析

## 📞 技术支持

如果遇到问题：

1. 查看浏览器控制台错误信息
2. 检查Redmine日志文件
3. 参考项目文档和README
4. 提交详细的Issue报告

---

**新架构带来的是更好的开发体验、更强的功能和更优的性能！** 🎉
