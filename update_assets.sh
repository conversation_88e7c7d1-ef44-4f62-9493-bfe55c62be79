#!/bin/bash

# 更新vue-office本地资源文件脚本
# 使用您提供的正确CDN地址和版本

echo "=== 更新vue-office本地资源文件 ==="
echo "使用最新版本: docx@1.6.3, excel@1.7.14, pdf@2.0.10, pptx@1.0.1"
echo

# 创建assets目录
echo "创建目录结构..."
mkdir -p assets/javascripts
mkdir -p assets/stylesheets

# 下载Vue.js
echo "1. 下载Vue.js..."
curl -L -o assets/javascripts/vue.global.js \
  https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.js

# 下载vue-office-docx (v1.6.3)
echo "2. 下载vue-office-docx v1.6.3..."
curl -L -o assets/javascripts/vue-office-docx.min.js \
  https://cdn.jsdelivr.net/npm/@vue-office/docx@1.6.3/lib/v3/index.min.js

curl -L -o assets/stylesheets/vue-office-docx.min.css \
  https://cdn.jsdelivr.net/npm/@vue-office/docx@1.6.3/lib/v3/index.min.css

# 下载vue-office-excel (v1.7.14)
echo "3. 下载vue-office-excel v1.7.14..."
curl -L -o assets/javascripts/vue-office-excel.umd.min.js \
  https://cdn.jsdelivr.net/npm/@vue-office/excel@1.7.14/lib/v2/vue-office-excel.umd.min.js

curl -L -o assets/stylesheets/vue-office-excel.min.css \
  https://cdn.jsdelivr.net/npm/@vue-office/excel@1.7.14/lib/v3/index.min.css

# 下载vue-office-pdf (v2.0.10)
echo "4. 下载vue-office-pdf v2.0.10..."
curl -L -o assets/javascripts/vue-office-pdf.umd.min.js \
  https://cdn.jsdelivr.net/npm/@vue-office/pdf@2.0.10/lib/v3/vue-office-pdf.umd.min.js

# 下载vue-office-pptx (v1.0.1)
echo "5. 下载vue-office-pptx v1.0.1..."
curl -L -o assets/javascripts/vue-office-pptx.esm.js \
  https://cdn.jsdelivr.net/npm/@vue-office/pptx@1.0.1/+esm

echo
echo "=== 下载完成 ==="

# 检查文件大小
echo "检查下载的文件："
echo
echo "JavaScript文件:"
ls -lh assets/javascripts/ | grep -E '\.(js)$'

echo
echo "CSS文件:"
ls -lh assets/stylesheets/ | grep -E '\.(css)$'

echo
echo "=== 文件验证 ==="

# 验证文件大小
check_file() {
    local file=$1
    local min_size=$2
    local description=$3
    
    if [ -f "$file" ]; then
        local size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo 0)
        if [ "$size" -gt "$min_size" ]; then
            echo "✅ $description - $(numfmt --to=iec $size)"
        else
            echo "❌ $description - 文件太小 ($(numfmt --to=iec $size))"
            return 1
        fi
    else
        echo "❌ $description - 文件不存在"
        return 1
    fi
    return 0
}

# 检查所有文件
all_ok=true

check_file "assets/javascripts/vue.global.js" 1000000 "Vue.js" || all_ok=false
check_file "assets/javascripts/vue-office-docx.min.js" 100000 "vue-office-docx" || all_ok=false
check_file "assets/javascripts/vue-office-excel.umd.min.js" 100000 "vue-office-excel" || all_ok=false
check_file "assets/javascripts/vue-office-pdf.umd.min.js" 50000 "vue-office-pdf" || all_ok=false
check_file "assets/javascripts/vue-office-pptx.esm.js" 10000 "vue-office-pptx" || all_ok=false
check_file "assets/stylesheets/vue-office-docx.min.css" 1000 "vue-office-docx CSS" || all_ok=false
check_file "assets/stylesheets/vue-office-excel.min.css" 1000 "vue-office-excel CSS" || all_ok=false

echo
if [ "$all_ok" = true ]; then
    echo "🎉 所有文件下载成功！"
    echo
    echo "下一步："
    echo "1. 重启Redmine服务器"
    echo "2. 在插件配置中选择 'Vue Office' 预览方法"
    echo "3. 测试文档预览功能"
else
    echo "⚠️  部分文件下载失败，请检查网络连接或手动下载"
    echo
    echo "手动下载地址："
    echo "Vue.js: https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.js"
    echo "docx: https://cdn.jsdelivr.net/npm/@vue-office/docx@1.6.3/lib/v3/index.min.js"
    echo "excel: https://cdn.jsdelivr.net/npm/@vue-office/excel@1.7.14/lib/v2/vue-office-excel.umd.min.js"
    echo "pdf: https://cdn.jsdelivr.net/npm/@vue-office/pdf@2.0.10/lib/v3/vue-office-pdf.umd.min.js"
    echo "pptx: https://cdn.jsdelivr.net/npm/@vue-office/pptx@1.0.1/+esm"
fi

echo
echo "=== 更新完成 ==="
