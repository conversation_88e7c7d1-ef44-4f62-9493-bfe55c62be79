# Redmine文档查看器插件 - 最终安装指南

## 🎯 解决方案概述

基于您遇到的Vue组件加载问题，我们提供了一个完整的解决方案，包含多种预览模式：

1. **Vue Office模式** - 完整的在线预览功能
2. **备用预览模式** - 兼容性最好，包含Microsoft Office Online Viewer
3. **kkFileView模式** - 企业级文档预览服务
4. **简化预览模式** - 基础的图片和PDF预览

## 🚀 快速安装（推荐）

### 步骤1: 复制插件文件
```bash
# 复制到Redmine插件目录
cp -r redmine_document_viewer /path/to/redmine/plugins/
chown -R redmine:redmine /path/to/redmine/plugins/redmine_document_viewer
```

### 步骤2: 复制本地资源文件
```bash
cd /path/to/redmine/plugins/redmine_document_viewer
./copy_from_cdn.sh
```

### 步骤3: 重启Redmine
```bash
sudo systemctl restart apache2  # 或nginx
```

### 步骤4: 配置插件
1. 登录Redmine管理界面
2. 进入 **管理** → **插件** → **Redmine Document Viewer Plugin** → **配置**
3. **选择预览方法为 "备用预览 (兼容性最好)"** ⭐
4. 启用需要的文件类型
5. 保存设置

## 📋 预览模式对比

| 预览模式 | 优势 | 劣势 | 推荐场景 |
|----------|------|------|----------|
| **备用预览** | 兼容性最好，无需额外配置 | 功能相对简单 | **推荐首选** |
| Vue Office | 功能最全，界面美观 | 需要正确配置资源文件 | 资源文件正常时 |
| kkFileView | 支持格式最多 | 需要部署额外服务 | 企业环境 |

## 🔧 备用预览模式功能

### 图片文件
- ✅ 直接在浏览器中显示
- ✅ 支持缩放和查看
- ✅ 错误处理和降级

### PDF文件
- ✅ 浏览器内置PDF查看器
- ✅ 支持缩放、翻页、搜索
- ✅ 兼容所有现代浏览器

### Office文档 (Word/Excel/PowerPoint)
- ✅ 自动尝试Microsoft Office Online Viewer
- ✅ 支持大部分Office格式
- ✅ 无需本地安装Office软件
- ✅ 降级到下载模式

## 🛠️ 故障排除

### 问题1: Vue组件错误
**现象**: 控制台显示 `Cannot read properties of undefined (reading 'defineComponent')`

**解决**: 
```bash
# 切换到备用预览模式
# 在插件配置中选择 "备用预览 (兼容性最好)"
```

### 问题2: 预览按钮不显示
**检查**:
```bash
# 检查JavaScript是否正确加载
# 查看浏览器控制台是否有错误
# 确认文件格式在支持列表中
```

### 问题3: Office文档无法预览
**解决**:
- 备用模式会自动尝试Microsoft Office Online Viewer
- 如果在线查看器不可用，会提供下载选项
- 确保网络可以访问 `view.officeapps.live.com`

## 📁 最终文件结构

```
plugins/redmine_document_viewer/
├── init.rb                              # 插件配置
├── app/
│   ├── controllers/document_viewer_controller.rb
│   ├── helpers/document_viewer_helper.rb
│   └── views/
│       └── document_viewer/
│           ├── vue_office_preview.html.erb    # Vue Office预览
│           ├── fallback_preview.html.erb      # 备用预览 ⭐
│           └── simple_preview.html.erb        # 简化预览
├── assets/
│   ├── javascripts/
│   │   ├── vue.global.js                # Vue.js (可选)
│   │   ├── vue-office-*.js              # Vue Office组件 (可选)
│   │   └── document_viewer.js           # 插件脚本
│   └── stylesheets/
│       └── document_viewer.css          # 插件样式
├── copy_from_cdn.sh                     # 资源复制脚本
├── check_assets.sh                      # 资源检查脚本
└── 文档/
    ├── FINAL_GUIDE.md                   # 本指南 ⭐
    ├── TROUBLESHOOTING.md               # 故障排除
    └── README.md                        # 完整说明
```

## ✅ 验证安装

### 1. 检查插件状态
- 在Redmine管理界面中看到插件
- 配置页面可以正常访问

### 2. 测试预览功能
- 上传一个Word/Excel/PDF文件
- 点击文件旁边的蓝色"预览"按钮
- 确认预览页面正常打开

### 3. 检查不同文件格式
- **图片**: 应该直接显示
- **PDF**: 在浏览器中打开
- **Office文档**: 尝试在线查看器或提供下载

## 🎉 成功标志

安装成功后，您应该看到：

- ✅ 所有附件旁边都有蓝色的"预览"按钮
- ✅ 图片文件可以直接预览
- ✅ PDF文件在浏览器中正常显示
- ✅ Office文档尝试在线预览或提供下载
- ✅ 无JavaScript错误

## 📞 技术支持

如果仍有问题：

1. **查看日志**:
   ```bash
   tail -f /path/to/redmine/log/production.log
   ```

2. **检查浏览器控制台**: 按F12查看错误信息

3. **尝试不同预览模式**: 在插件配置中切换预览方法

4. **联系支持**: 提供详细的错误信息和环境描述

## 🔄 升级和维护

### 定期检查
```bash
# 检查资源文件状态
./check_assets.sh

# 更新资源文件
./copy_from_cdn.sh
```

### 性能优化
- 启用Web服务器的gzip压缩
- 设置适当的缓存头
- 定期清理Redmine缓存

## 🎯 推荐配置

**生产环境推荐设置**:
- 预览方法: **备用预览 (兼容性最好)**
- 启用所有文件类型预览
- 最大文件大小: 50MB
- 启用预览缓存

这个配置提供了最好的兼容性和稳定性，适合大多数部署环境。

---

**恭喜！您已经成功安装了Redmine文档查看器插件！** 🎉

现在您可以在Redmine中方便地预览各种格式的文档了。
