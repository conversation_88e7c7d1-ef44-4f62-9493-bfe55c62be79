#!/bin/bash

# 检查本地资源文件配置脚本

echo "=== Redmine文档查看器插件 - 资源文件检查 ==="
echo

# 检查目录结构
echo "1. 检查目录结构..."
if [ -d "assets/javascripts" ] && [ -d "assets/stylesheets" ]; then
    echo "✅ 目录结构正确"
else
    echo "❌ 目录结构不正确，请确保存在 assets/javascripts 和 assets/stylesheets 目录"
    exit 1
fi

echo

# 检查JavaScript文件
echo "2. 检查JavaScript文件..."

js_files=(
    "vue.global.js"
    "vue-office-docx.umd.js"
    "vue-office-excel.umd.js"
    "vue-office-pdf.umd.js"
)

js_all_ok=true

for file in "${js_files[@]}"; do
    file_path="assets/javascripts/$file"
    if [ -f "$file_path" ]; then
        file_size=$(stat -f%z "$file_path" 2>/dev/null || stat -c%s "$file_path" 2>/dev/null || echo 0)
        if [ "$file_size" -gt 1000 ]; then
            echo "✅ $file ($(numfmt --to=iec $file_size))"
        else
            echo "❌ $file 文件太小 ($(numfmt --to=iec $file_size))，可能下载失败"
            js_all_ok=false
        fi
    else
        echo "❌ $file 文件不存在"
        js_all_ok=false
    fi
done

echo

# 检查CSS文件
echo "3. 检查CSS文件..."

css_files=(
    "vue-office-docx.css"
    "vue-office-excel.css"
    "vue-office-pdf.css"
)

css_all_ok=true

for file in "${css_files[@]}"; do
    file_path="assets/stylesheets/$file"
    if [ -f "$file_path" ]; then
        file_size=$(stat -f%z "$file_path" 2>/dev/null || stat -c%s "$file_path" 2>/dev/null || echo 0)
        echo "✅ $file ($(numfmt --to=iec $file_size))"
    else
        echo "❌ $file 文件不存在"
        css_all_ok=false
    fi
done

echo

# 检查文件内容
echo "4. 检查文件内容..."

# 检查Vue.js文件
vue_file="assets/javascripts/vue.global.js"
if [ -f "$vue_file" ]; then
    first_line=$(head -n 1 "$vue_file")
    if [[ "$first_line" == *"Vue"* ]] || [[ "$first_line" == *"function"* ]] || [[ "$first_line" == *"!"* ]]; then
        echo "✅ Vue.js 文件内容正确"
    else
        echo "❌ Vue.js 文件内容异常: $first_line"
        js_all_ok=false
    fi
fi

echo

# 总结
echo "5. 检查结果总结..."

if [ "$js_all_ok" = true ] && [ "$css_all_ok" = true ]; then
    echo "🎉 所有资源文件配置正确！"
    echo
    echo "下一步："
    echo "1. 重启Redmine服务器"
    echo "2. 在插件配置中选择 'Vue Office' 预览方法"
    echo "3. 测试文档预览功能"
else
    echo "⚠️  存在问题，需要修复："
    echo
    if [ "$js_all_ok" = false ]; then
        echo "JavaScript文件问题："
        echo "- 运行 ./download_assets.sh 重新下载"
        echo "- 或手动下载文件（参考 LOCAL_ASSETS.md）"
    fi
    if [ "$css_all_ok" = false ]; then
        echo "CSS文件问题："
        echo "- 运行 ./download_assets.sh 重新下载"
        echo "- 或手动下载文件（参考 LOCAL_ASSETS.md）"
    fi
    echo
    echo "详细说明请查看 LOCAL_ASSETS.md 文件"
fi

echo
echo "=== 检查完成 ==="
