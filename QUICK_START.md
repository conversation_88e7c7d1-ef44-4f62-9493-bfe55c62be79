# 快速开始指南

## 🚀 5分钟快速安装

### 步骤1: 安装插件

```bash
# 复制插件到Redmine插件目录
cp -r redmine_document_viewer /path/to/redmine/plugins/

# 设置权限
chown -R redmine:redmine /path/to/redmine/plugins/redmine_document_viewer
chmod -R 755 /path/to/redmine/plugins/redmine_document_viewer
```

### 步骤2: 更新本地资源

选择以下任一方式更新vue-office资源文件：

#### 方式A: 自动下载（推荐）
```bash
cd /path/to/redmine/plugins/redmine_document_viewer
chmod +x update_assets.sh
./update_assets.sh
```

#### 方式B: 从本地CDN复制
```bash
cd /path/to/redmine/plugins/redmine_document_viewer
chmod +x copy_local_assets.sh
./copy_local_assets.sh /path/to/your/cdn/directory
```

#### 方式C: 手动下载
参考 `MANUAL_DOWNLOAD.md` 文件

### 步骤3: 验证资源文件

```bash
cd /path/to/redmine/plugins/redmine_document_viewer
chmod +x check_assets.sh
./check_assets.sh
```

**预期结果**：
```
✅ Vue.js (1.4M)
✅ vue-office-docx (200K)
✅ vue-office-excel (300K)
✅ vue-office-pdf (150K)
✅ vue-office-pptx (100K)
✅ vue-office-docx CSS (10K)
✅ vue-office-excel CSS (15K)
```

### 步骤4: 重启Redmine

```bash
# Apache
sudo systemctl restart apache2

# Nginx
sudo systemctl restart nginx

# Passenger
touch /path/to/redmine/tmp/restart.txt
```

### 步骤5: 配置插件

1. 以管理员身份登录Redmine
2. 进入 **管理** → **插件**
3. 找到 **Redmine Document Viewer Plugin**
4. 点击 **配置**
5. 选择预览方法为 **"Vue Office"**
6. 启用需要的文件类型预览
7. 保存设置

### 步骤6: 测试功能

1. 进入任意项目的问题页面
2. 上传一个Word/Excel/PDF文件
3. 查看文件旁边是否显示蓝色的 **"预览"** 按钮
4. 点击预览按钮测试功能

## 📋 支持的文件格式

| 格式 | 扩展名 | 预览方式 |
|------|--------|----------|
| 图片 | jpg, jpeg, png, gif, bmp, webp | 直接显示 |
| PDF | pdf | 浏览器内置预览 |
| Word | doc, docx, rtf | vue-office组件 |
| Excel | xls, xlsx, csv | vue-office组件 |
| PowerPoint | ppt, pptx | vue-office组件 |

## 🔧 故障排除

### 问题1: 看不到预览按钮
**检查**：
- 插件是否正确安装
- 文件格式是否支持
- 用户是否有权限

**解决**：
```bash
# 检查插件状态
ls -la /path/to/redmine/plugins/redmine_document_viewer

# 重启服务器
sudo systemctl restart apache2

# 清除浏览器缓存
```

### 问题2: 预览页面显示"配置在线预览"
**原因**：vue-office文件未正确下载

**解决**：
```bash
cd /path/to/redmine/plugins/redmine_document_viewer
./check_assets.sh
# 如果文件缺失，重新运行 ./update_assets.sh
```

### 问题3: 预览功能报错
**检查**：
- 浏览器控制台错误信息
- Redmine日志文件

**解决**：
```bash
# 查看Redmine日志
tail -f /path/to/redmine/log/production.log

# 检查文件权限
ls -la assets/javascripts/
```

## 📁 文件结构

安装完成后的目录结构：

```
plugins/redmine_document_viewer/
├── init.rb                              # 插件初始化
├── config/routes.rb                     # 路由配置
├── app/                                 # 应用代码
│   ├── controllers/
│   ├── helpers/
│   └── views/
├── assets/                              # 本地资源
│   ├── javascripts/
│   │   ├── vue.global.js               # Vue.js核心
│   │   ├── vue-office-docx.min.js      # Word预览
│   │   ├── vue-office-excel.umd.min.js # Excel预览
│   │   ├── vue-office-pdf.umd.min.js   # PDF预览
│   │   ├── vue-office-pptx.esm.js      # PowerPoint预览
│   │   └── document_viewer.js          # 插件脚本
│   └── stylesheets/
│       ├── vue-office-docx.min.css     # Word样式
│       ├── vue-office-excel.min.css    # Excel样式
│       └── document_viewer.css         # 插件样式
├── lib/document_viewer_hooks.rb         # 视图钩子
├── update_assets.sh                     # 资源更新脚本
├── copy_local_assets.sh                 # 本地复制脚本
├── check_assets.sh                      # 资源检查脚本
└── README.md                            # 说明文档
```

## 🎯 版本信息

- **Vue.js**: 3.x
- **vue-office-docx**: 1.6.3
- **vue-office-excel**: 1.7.14
- **vue-office-pdf**: 2.0.10
- **vue-office-pptx**: 1.0.1

## 📞 获取帮助

如果遇到问题：

1. 查看 `LOCAL_ASSETS.md` - 本地化配置详细说明
2. 查看 `MANUAL_DOWNLOAD.md` - 手动下载指南
3. 查看 `USAGE.md` - 使用说明
4. 检查Redmine日志文件
5. 提交Issue到项目仓库

## ✅ 成功标志

安装成功后，您应该看到：

- ✅ 插件出现在Redmine管理界面
- ✅ 配置页面可以正常访问
- ✅ 附件旁边显示蓝色预览按钮
- ✅ 点击预览按钮能正常打开文档
- ✅ 不同格式文档都能正确预览

恭喜！您已经成功安装了Redmine文档查看器插件！🎉
