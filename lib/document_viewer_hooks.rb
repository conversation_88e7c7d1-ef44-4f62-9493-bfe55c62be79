class DocumentViewerHooks < Redmine::Hook::ViewListener
  include ActionView::Helpers::TagHelper
  include ActionView::Helpers::UrlHelper
  include ApplicationHelper

  # 在页面头部添加CSS和JavaScript
  def view_layouts_base_html_head(context={})
    # 添加CSS和JavaScript
    stylesheet_link_tag('document_viewer', :plugin => 'redmine_document_viewer') +
    javascript_include_tag('document_viewer', :plugin => 'redmine_document_viewer')
  end

  # 在附件显示区域添加预览按钮
  def view_attachments_form_bottom(context={})
    # 这个钩子在附件表单底部触发，我们可以在这里添加预览功能的说明
    content_tag(:div, :class => 'document-viewer-info') do
      content_tag(:p, '支持在线预览Word、Excel、PDF、图片等格式的文档', :class => 'info')
    end
  end

  # 自定义方法：为附件添加预览按钮
  def attachment_preview_link(attachment)
    return '' unless attachment.previewable?

    link_to('预览',
            preview_document_viewer_path(attachment),
            :class => 'icon icon-preview document-preview-btn',
            :target => '_blank',
            :title => '在线预览文档')
  end

  # 在附件列表中添加预览按钮
  def view_attachments_links(context={})
    attachments = context[:attachments] || []
    return '' if attachments.empty?

    content = ''
    attachments.each do |attachment|
      if attachment.previewable?
        content += content_tag(:span, :class => 'document-preview-wrapper') do
          link_to('预览',
                  preview_document_viewer_path(attachment),
                  :class => 'icon icon-preview document-preview-link',
                  :target => '_blank',
                  :title => "预览 #{attachment.filename}")
        end
      end
    end

    content.html_safe
  end
end
