#!/bin/bash

# 从本地CDN目录复制vue-office资源文件脚本
# 如果您已经下载了这些文件到本地

echo "=== 从本地复制vue-office资源文件 ==="
echo

# 检查参数
if [ $# -eq 0 ]; then
    echo "用法: $0 <本地CDN目录路径>"
    echo "例如: $0 /path/to/cdn"
    echo
    echo "CDN目录应包含以下文件:"
    echo "├── vue@3/dist/vue.global.js"
    echo "├── @vue-office/docx@1.6.3/lib/v3/index.min.js"
    echo "├── @vue-office/docx@1.6.3/lib/v3/index.min.css"
    echo "├── @vue-office/excel@1.7.14/lib/v2/vue-office-excel.umd.min.js"
    echo "├── @vue-office/excel@1.7.14/lib/v3/index.min.css"
    echo "├── @vue-office/pdf@2.0.10/lib/v3/vue-office-pdf.umd.min.js"
    echo "└── @vue-office/pptx@1.0.1/+esm"
    exit 1
fi

CDN_DIR="$1"

# 检查CDN目录是否存在
if [ ! -d "$CDN_DIR" ]; then
    echo "❌ 错误: CDN目录不存在: $CDN_DIR"
    exit 1
fi

# 创建assets目录
echo "创建目录结构..."
mkdir -p assets/javascripts
mkdir -p assets/stylesheets

# 定义文件映射 - 根据实际的CDN目录结构
declare -A file_map=(
    ["$CDN_DIR/docx-1.6.3/lib/v3/index.js"]="assets/javascripts/vue-office-docx.min.js"
    ["$CDN_DIR/docx-1.6.3/lib/v3/index.css"]="assets/stylesheets/vue-office-docx.min.css"
    ["$CDN_DIR/excel-1.7.14/lib/v2/vue-office-excel.umd.js"]="assets/javascripts/vue-office-excel.umd.min.js"
    ["$CDN_DIR/excel-1.7.14/lib/v3/index.css"]="assets/stylesheets/vue-office-excel.min.css"
    ["$CDN_DIR/pdf-2.0.10/lib/v3/vue-office-pdf.umd.js"]="assets/javascripts/vue-office-pdf.umd.min.js"
    ["$CDN_DIR/ppt-1.0.1/lib/v3/vue-office-pptx.umd.js"]="assets/javascripts/vue-office-pptx.esm.js"
)

# Vue.js需要单独处理，如果CDN目录中没有，则从网络下载
vue_file="$CDN_DIR/vue@3/dist/vue.global.js"
if [ ! -f "$vue_file" ]; then
    echo "Vue.js文件不在CDN目录中，尝试从网络下载..."
    curl -L -o assets/javascripts/vue.global.js https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.js
    if [ $? -eq 0 ]; then
        echo "✅ Vue.js下载成功"
    else
        echo "❌ Vue.js下载失败"
        all_ok=false
    fi
else
    cp "$vue_file" "assets/javascripts/vue.global.js"
    echo "✅ 复制成功: vue.global.js"
fi

# 复制文件
echo "复制文件..."
all_ok=true

for source in "${!file_map[@]}"; do
    target="${file_map[$source]}"
    filename=$(basename "$target")

    if [ -f "$source" ]; then
        cp "$source" "$target"
        if [ $? -eq 0 ]; then
            echo "✅ 复制成功: $filename"
        else
            echo "❌ 复制失败: $filename"
            all_ok=false
        fi
    else
        echo "❌ 源文件不存在: $source"
        all_ok=false
    fi
done

echo
echo "=== 复制完成 ==="

# 检查文件大小
echo "检查复制的文件："
echo
echo "JavaScript文件:"
ls -lh assets/javascripts/ 2>/dev/null | grep -E '\.(js)$' || echo "无JavaScript文件"

echo
echo "CSS文件:"
ls -lh assets/stylesheets/ 2>/dev/null | grep -E '\.(css)$' || echo "无CSS文件"

echo
echo "=== 文件验证 ==="

# 验证文件大小
check_file() {
    local file=$1
    local min_size=$2
    local description=$3

    if [ -f "$file" ]; then
        local size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo 0)
        if [ "$size" -gt "$min_size" ]; then
            echo "✅ $description - $(numfmt --to=iec $size)"
        else
            echo "❌ $description - 文件太小 ($(numfmt --to=iec $size))"
            return 1
        fi
    else
        echo "❌ $description - 文件不存在"
        return 1
    fi
    return 0
}

# 检查所有文件
check_file "assets/javascripts/vue.global.js" 1000000 "Vue.js" || all_ok=false
check_file "assets/javascripts/vue-office-docx.min.js" 100000 "vue-office-docx" || all_ok=false
check_file "assets/javascripts/vue-office-excel.umd.min.js" 100000 "vue-office-excel" || all_ok=false
check_file "assets/javascripts/vue-office-pdf.umd.min.js" 50000 "vue-office-pdf" || all_ok=false
check_file "assets/javascripts/vue-office-pptx.esm.js" 10000 "vue-office-pptx" || all_ok=false
check_file "assets/stylesheets/vue-office-docx.min.css" 1000 "vue-office-docx CSS" || all_ok=false
check_file "assets/stylesheets/vue-office-excel.min.css" 1000 "vue-office-excel CSS" || all_ok=false

echo
if [ "$all_ok" = true ]; then
    echo "🎉 所有文件复制成功！"
    echo
    echo "下一步："
    echo "1. 重启Redmine服务器"
    echo "2. 在插件配置中选择 'Vue Office' 预览方法"
    echo "3. 测试文档预览功能"
else
    echo "⚠️  部分文件复制失败，请检查源文件路径"
fi

echo
echo "=== 复制完成 ==="
