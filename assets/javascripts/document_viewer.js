// 文档查看器JavaScript功能

(function() {
  'use strict';

  // 支持预览的文件扩展名
  const PREVIEWABLE_EXTENSIONS = [
    'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp',  // 图片
    'pdf',                                        // PDF
    'doc', 'docx', 'rtf',                        // Word
    'xls', 'xlsx', 'csv',                        // Excel
    'ppt', 'pptx'                                // PowerPoint
  ];

  // 检查文件是否支持预览
  function isPreviewable(filename) {
    if (!filename) return false;

    const extension = filename.split('.').pop().toLowerCase();
    return PREVIEWABLE_EXTENSIONS.includes(extension);
  }

  // 获取附件ID从链接中
  function getAttachmentId(link) {
    const href = link.getAttribute('href');
    if (!href) return null;

    // 匹配 /attachments/123/filename 或 /attachments/download/123/filename
    const match = href.match(/\/attachments\/(?:download\/)?(\d+)/);
    return match ? match[1] : null;
  }

  // 创建预览按钮
  function createPreviewButton(attachmentId, filename) {
    const button = document.createElement('a');
    button.href = '/doc_viewer/' + attachmentId + '/preview';
    button.target = '_blank';
    button.className = 'icon icon-preview document-preview-btn';
    button.title = '预览 ' + filename;
    button.textContent = '预览';
    button.style.marginLeft = '10px';
    button.style.color = '#3498db';
    button.style.textDecoration = 'none';

    return button;
  }

  // 为附件链接添加预览按钮
  function addPreviewButtons() {
    // 查找所有附件链接
    const attachmentLinks = document.querySelectorAll('a[href*="/attachments/"]');

    attachmentLinks.forEach(function(link) {
      // 避免重复添加
      if (link.parentNode.querySelector('.document-preview-btn')) {
        return;
      }

      const attachmentId = getAttachmentId(link);
      if (!attachmentId) return;

      // 从链接文本或href中获取文件名
      let filename = link.textContent.trim();
      if (!filename) {
        const href = link.getAttribute('href');
        const parts = href.split('/');
        filename = parts[parts.length - 1];
      }

      // 检查是否支持预览
      if (isPreviewable(filename)) {
        const previewButton = createPreviewButton(attachmentId, filename);

        // 在附件链接后插入预览按钮
        if (link.nextSibling) {
          link.parentNode.insertBefore(previewButton, link.nextSibling);
        } else {
          link.parentNode.appendChild(previewButton);
        }
      }
    });
  }

  // 为表格中的附件添加预览按钮
  function addPreviewButtonsToTable() {
    const attachmentTables = document.querySelectorAll('table.list.files, table.list.attachments');

    attachmentTables.forEach(function(table) {
      const rows = table.querySelectorAll('tbody tr');

      rows.forEach(function(row) {
        const attachmentLink = row.querySelector('a[href*="/attachments/"]');
        if (!attachmentLink) return;

        // 避免重复添加
        if (row.querySelector('.document-preview-btn')) return;

        const attachmentId = getAttachmentId(attachmentLink);
        if (!attachmentId) return;

        const filename = attachmentLink.textContent.trim();

        if (isPreviewable(filename)) {
          const previewButton = createPreviewButton(attachmentId, filename);
          previewButton.style.marginLeft = '5px';
          previewButton.style.fontSize = '12px';

          // 在最后一个单元格中添加预览按钮
          const lastCell = row.querySelector('td:last-child');
          if (lastCell) {
            lastCell.appendChild(previewButton);
          }
        }
      });
    });
  }

  // 初始化函数
  function init() {
    addPreviewButtons();
    addPreviewButtonsToTable();
  }

  // 页面加载完成后执行
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }

  // 监听AJAX请求完成，重新添加预览按钮
  if (typeof jQuery !== 'undefined') {
    jQuery(document).ajaxComplete(function() {
      setTimeout(init, 100);
    });
  }

  // 为动态加载的内容添加预览按钮
  if (window.MutationObserver) {
    const observer = new MutationObserver(function(mutations) {
      let shouldUpdate = false;

      mutations.forEach(function(mutation) {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          for (let i = 0; i < mutation.addedNodes.length; i++) {
            const node = mutation.addedNodes[i];
            if (node.nodeType === Node.ELEMENT_NODE) {
              if (node.querySelector && node.querySelector('a[href*="/attachments/"]')) {
                shouldUpdate = true;
                break;
              }
            }
          }
        }
      });

      if (shouldUpdate) {
        setTimeout(init, 100);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

})();
