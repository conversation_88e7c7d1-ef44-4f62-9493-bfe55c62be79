/* 文档查看器样式 */

.document-viewer-container {
  margin: 20px 0;
}

.document-filters {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
  border: 1px solid #e9ecef;
}

.document-filters label {
  font-weight: bold;
  margin-right: 10px;
}

.document-filters select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
}

.document-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.document-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.document-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.document-thumbnail {
  height: 120px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.document-link {
  display: block;
  width: 100%;
  height: 100%;
  text-decoration: none;
}

.file-icon {
  font-size: 48px;
  color: white;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.icon-image { background: linear-gradient(135deg, #ff6b6b, #ee5a24); }
.icon-pdf { background: linear-gradient(135deg, #e74c3c, #c0392b); }
.icon-word { background: linear-gradient(135deg, #3498db, #2980b9); }
.icon-excel { background: linear-gradient(135deg, #27ae60, #229954); }
.icon-powerpoint { background: linear-gradient(135deg, #f39c12, #e67e22); }
.icon-file { background: linear-gradient(135deg, #95a5a6, #7f8c8d); }

.document-info {
  padding: 15px;
}

.document-title {
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.3;
}

.document-title a {
  color: #2c3e50;
  text-decoration: none;
}

.document-title a:hover {
  color: #3498db;
}

.document-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 12px;
  color: #6c757d;
}

.file-size {
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 3px;
}

.upload-date {
  font-style: italic;
}

.document-container-info {
  margin-bottom: 15px;
  font-size: 13px;
}

.container-link {
  color: #6c757d;
  text-decoration: none;
  padding: 2px 6px;
  background: #f8f9fa;
  border-radius: 3px;
  border: 1px solid #e9ecef;
}

.container-link:hover {
  color: #495057;
  background: #e9ecef;
}

.document-actions {
  display: flex;
  gap: 8px;
}

.btn {
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  text-decoration: none;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  flex: 1;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
  color: white;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
  color: white;
}

.btn-sm {
  padding: 6px 10px;
  font-size: 11px;
}

/* 预览页面样式 */
.preview-container {
  max-width: 100%;
  margin: 0 auto;
}

.preview-header {
  background: #2c3e50;
  color: white;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.preview-title {
  margin: 0;
  font-size: 18px;
  word-break: break-word;
}

.preview-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.preview-content {
  background: white;
  min-height: 600px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .document-grid {
    grid-template-columns: 1fr;
  }
  
  .document-filters {
    text-align: center;
  }
  
  .document-filters label {
    display: block;
    margin-bottom: 5px;
  }
  
  .preview-header {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
  
  .preview-actions {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .document-card {
    margin: 0 10px;
  }
  
  .document-actions {
    flex-direction: column;
  }
  
  .btn {
    text-align: center;
  }
}

/* 加载和错误状态 */
.loading, .error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400px;
  font-size: 16px;
  text-align: center;
}

.loading {
  color: #6c757d;
}

.loading::before {
  content: "⏳";
  font-size: 48px;
  margin-bottom: 20px;
  animation: pulse 2s infinite;
}

.error {
  color: #e74c3c;
}

.error::before {
  content: "❌";
  font-size: 48px;
  margin-bottom: 20px;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 无数据状态 */
.nodata {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 40px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

/* 设置页面样式 */
.settings fieldset {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
}

.settings legend {
  font-weight: bold;
  color: #2c3e50;
  padding: 0 10px;
}

.settings p {
  margin-bottom: 10px;
}

.settings label {
  display: inline-block;
  margin-bottom: 5px;
  font-weight: 500;
}

.settings input[type="text"],
.settings input[type="password"],
.settings input[type="number"],
.settings select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.settings em {
  display: block;
  font-size: 12px;
  color: #6c757d;
  margin-top: 5px;
}
