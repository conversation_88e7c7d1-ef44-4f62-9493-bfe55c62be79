# 手动下载vue-office资源指南

由于网络环境限制，自动下载脚本可能无法正常工作。本指南提供手动下载的详细步骤。

## 需要下载的文件

### JavaScript文件（保存到 `assets/javascripts/` 目录）

1. **Vue.js 核心库**
   - 文件名: `vue.global.js`
   - 下载地址: https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.js
   - 备用地址: https://unpkg.com/vue@3/dist/vue.global.js
   - 文件大小: 约1.4MB

2. **Word文档预览组件**
   - 文件名: `vue-office-docx.min.js`
   - 下载地址: https://cdn.jsdelivr.net/npm/@vue-office/docx@1.6.3/lib/v3/index.min.js
   - 文件大小: 约200KB

3. **Excel表格预览组件**
   - 文件名: `vue-office-excel.umd.min.js`
   - 下载地址: https://cdn.jsdelivr.net/npm/@vue-office/excel@1.7.14/lib/v2/vue-office-excel.umd.min.js
   - 文件大小: 约300KB

4. **PDF文档预览组件**
   - 文件名: `vue-office-pdf.umd.min.js`
   - 下载地址: https://cdn.jsdelivr.net/npm/@vue-office/pdf@2.0.10/lib/v3/vue-office-pdf.umd.min.js
   - 文件大小: 约150KB

5. **PowerPoint预览组件（可选）**
   - 文件名: `vue-office-pptx.esm.js`
   - 下载地址: https://cdn.jsdelivr.net/npm/@vue-office/pptx@1.0.1/+esm
   - 文件大小: 约100KB

### CSS样式文件（保存到 `assets/stylesheets/` 目录）

1. **Word文档样式**
   - 文件名: `vue-office-docx.min.css`
   - 下载地址: https://cdn.jsdelivr.net/npm/@vue-office/docx@1.6.3/lib/v3/index.min.css
   - 文件大小: 约10KB

2. **Excel表格样式**
   - 文件名: `vue-office-excel.min.css`
   - 下载地址: https://cdn.jsdelivr.net/npm/@vue-office/excel@1.7.14/lib/v3/index.min.css
   - 文件大小: 约15KB

## 下载步骤

### 方法一：使用浏览器下载

1. **创建目录结构**：
   ```bash
   cd /path/to/redmine/plugins/redmine_document_viewer
   mkdir -p assets/javascripts
   mkdir -p assets/stylesheets
   ```

2. **逐个下载文件**：
   - 在浏览器中打开上述每个下载地址
   - 右键点击页面 → "另存为"
   - 保存到对应的目录，使用指定的文件名

### 方法二：使用wget命令

```bash
cd /path/to/redmine/plugins/redmine_document_viewer

# 创建目录
mkdir -p assets/javascripts assets/stylesheets

# 下载JavaScript文件
wget -O assets/javascripts/vue.global.js https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.js
wget -O assets/javascripts/vue-office-docx.min.js https://cdn.jsdelivr.net/npm/@vue-office/docx@1.6.3/lib/v3/index.min.js
wget -O assets/javascripts/vue-office-excel.umd.min.js https://cdn.jsdelivr.net/npm/@vue-office/excel@1.7.14/lib/v2/vue-office-excel.umd.min.js
wget -O assets/javascripts/vue-office-pdf.umd.min.js https://cdn.jsdelivr.net/npm/@vue-office/pdf@2.0.10/lib/v3/vue-office-pdf.umd.min.js

# 下载CSS文件
wget -O assets/stylesheets/vue-office-docx.min.css https://cdn.jsdelivr.net/npm/@vue-office/docx@1.6.3/lib/v3/index.min.css
wget -O assets/stylesheets/vue-office-excel.min.css https://cdn.jsdelivr.net/npm/@vue-office/excel@1.7.14/lib/v3/index.min.css
```

### 方法三：使用curl命令

```bash
cd /path/to/redmine/plugins/redmine_document_viewer

# 创建目录
mkdir -p assets/javascripts assets/stylesheets

# 下载JavaScript文件
curl -L -o assets/javascripts/vue.global.js https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.js
curl -L -o assets/javascripts/vue-office-docx.min.js https://cdn.jsdelivr.net/npm/@vue-office/docx@1.6.3/lib/v3/index.min.js
curl -L -o assets/javascripts/vue-office-excel.umd.min.js https://cdn.jsdelivr.net/npm/@vue-office/excel@1.7.14/lib/v2/vue-office-excel.umd.min.js
curl -L -o assets/javascripts/vue-office-pdf.umd.min.js https://cdn.jsdelivr.net/npm/@vue-office/pdf@2.0.10/lib/v3/vue-office-pdf.umd.min.js

# 下载CSS文件
curl -L -o assets/stylesheets/vue-office-docx.min.css https://cdn.jsdelivr.net/npm/@vue-office/docx@1.6.3/lib/v3/index.min.css
curl -L -o assets/stylesheets/vue-office-excel.min.css https://cdn.jsdelivr.net/npm/@vue-office/excel@1.7.14/lib/v3/index.min.css
```

## 验证下载结果

下载完成后，运行检查脚本验证文件：

```bash
chmod +x check_assets.sh
./check_assets.sh
```

或手动检查文件大小：

```bash
ls -lh assets/javascripts/
ls -lh assets/stylesheets/
```

**预期结果**：
- vue.global.js 应该大于1MB
- 其他JavaScript文件应该大于50KB
- CSS文件应该大于5KB

## 备用镜像源

如果jsdelivr无法访问，可以尝试以下镜像源：

### 国内镜像
- https://cdn.bootcdn.net/
- https://cdn.staticfile.org/
- https://unpkg.com/

### 国外镜像
- https://unpkg.com/
- https://cdnjs.cloudflare.com/
- https://cdn.skypack.dev/

## 离线传输

对于完全无法访问外网的环境：

1. **在有网络的机器上下载**：
   - 按照上述步骤下载所有文件
   - 将整个 `assets` 目录打包

2. **传输到目标服务器**：
   ```bash
   # 打包
   tar -czf vue-office-assets.tar.gz assets/
   
   # 在目标服务器解压
   tar -xzf vue-office-assets.tar.gz
   ```

## 故障排除

### 问题1：下载的文件很小（几十字节）
**原因**：下载的是重定向页面而不是实际文件
**解决**：检查URL是否正确，或尝试其他镜像源

### 问题2：文件下载失败
**原因**：网络连接问题或CDN不可访问
**解决**：
1. 检查网络连接
2. 尝试使用代理
3. 使用其他镜像源
4. 采用离线传输方式

### 问题3：下载的文件无法使用
**原因**：文件损坏或版本不兼容
**解决**：
1. 重新下载文件
2. 检查文件完整性
3. 确认版本号是否正确

## 完成后的步骤

1. **重启Redmine服务器**
2. **配置插件**：在管理界面选择"Vue Office"预览方法
3. **测试功能**：上传文档并测试预览功能

如果遇到问题，请参考 `LOCAL_ASSETS.md` 文件获取更多帮助。
