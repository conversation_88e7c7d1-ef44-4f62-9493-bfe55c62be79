# 使用说明

## 插件效果展示

### 1. 问题页面中的预览按钮

在问题页面的附件列表中，每个支持预览的文件旁边会自动出现蓝色的"预览"按钮：

```
📎 报告.docx (2.3 MB) [预览] [下载]
📎 数据表.xlsx (1.8 MB) [预览] [下载]  
📎 演示文稿.pptx (5.2 MB) [预览] [下载]
📎 截图.png (856 KB) [预览] [下载]
```

### 2. 文档页面中的预览按钮

在项目文档页面的附件中也会显示预览按钮：

```
项目文档
├── 需求文档.docx [预览]
├── 设计图.pdf [预览]
└── 用户手册.docx [预览]
```

### 3. Wiki页面中的预览按钮

在Wiki页面的附件列表中同样会显示预览按钮。

## 预览界面

点击预览按钮后，会在新窗口中打开预览界面：

### Word文档预览
- 显示完整的文档内容
- 保持原有格式
- 支持滚动浏览

### Excel表格预览  
- 显示工作表标签
- 支持切换不同工作表
- 保持表格格式

### PDF文档预览
- 显示完整PDF内容
- 支持缩放和翻页
- 保持原有布局

### 图片预览
- 高清显示图片
- 支持缩放查看
- 自适应窗口大小

## 支持的操作

在预览界面中，您可以：

1. **查看文档内容** - 完整浏览文档
2. **下载原文件** - 点击下载按钮获取原始文件
3. **关闭预览** - 关闭预览窗口返回原页面

## 智能识别

插件会智能识别文件类型：

- ✅ **支持预览的格式**：显示预览按钮
- ❌ **不支持的格式**：不显示预览按钮

### 支持预览的文件格式：
- 图片：jpg, jpeg, png, gif, bmp, webp
- 文档：pdf, doc, docx, rtf
- 表格：xls, xlsx, csv  
- 演示：ppt, pptx

### 不支持预览的文件格式：
- 压缩包：zip, rar, 7z
- 可执行文件：exe, dmg
- 其他格式：txt, xml, json等

## 权限控制

预览功能完全遵循Redmine的权限体系：

- 用户只能预览有权限查看的附件
- 如果用户无权查看某个附件，则不会显示预览按钮
- 预览权限与原有的附件查看权限一致

## 性能优化

- 预览按钮通过JavaScript动态添加，不影响页面加载速度
- 支持AJAX页面更新后自动添加预览按钮
- 预览内容按需加载，节省带宽

## 移动端支持

- 预览按钮在移动设备上正常显示
- 预览界面适配移动端屏幕
- 触摸操作友好

## 常见问题

### Q: 为什么某些文件没有预览按钮？
A: 可能的原因：
1. 文件格式不在支持列表中
2. 文件大小超过限制（默认50MB）
3. 用户没有查看该附件的权限
4. 插件配置中禁用了该文件类型的预览

### Q: 预览按钮点击后没有反应？
A: 请检查：
1. 浏览器是否阻止了弹窗
2. 网络连接是否正常
3. 浏览器控制台是否有错误信息

### Q: 预览速度很慢？
A: 可以尝试：
1. 启用预览缓存功能
2. 检查网络连接速度
3. 考虑使用CDN加速

### Q: 某些Office文档预览效果不好？
A: 建议：
1. 确保文档格式标准
2. 考虑切换到kkFileView预览方式
3. 检查文档是否有特殊字体或格式
