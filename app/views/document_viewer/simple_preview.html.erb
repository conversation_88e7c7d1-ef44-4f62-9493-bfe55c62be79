<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>文档预览 - <%= @attachment.filename %></title>
  
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: Arial, sans-serif;
      background-color: #f5f5f5;
    }
    
    .preview-container {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      overflow: hidden;
      margin: 20px;
    }
    
    .preview-header {
      background: #2c3e50;
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .preview-title {
      margin: 0;
      font-size: 18px;
    }
    
    .preview-actions {
      display: flex;
      gap: 10px;
    }
    
    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      text-decoration: none;
      font-size: 14px;
    }
    
    .btn-primary {
      background: #3498db;
      color: white;
    }
    
    .btn-secondary {
      background: #95a5a6;
      color: white;
    }
    
    .preview-content {
      height: calc(100vh - 120px);
      min-height: 600px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .preview-frame {
      width: 100%;
      height: 100%;
      border: none;
    }
    
    .preview-image {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
    }
    
    .preview-message {
      text-align: center;
      padding: 40px;
      color: #666;
    }
    
    .preview-message h3 {
      margin-bottom: 20px;
      color: #333;
    }
    
    .file-info {
      background: #f8f9fa;
      padding: 15px;
      border-bottom: 1px solid #e9ecef;
    }
    
    .file-info span {
      margin-right: 20px;
      color: #666;
    }
  </style>
</head>
<body>
  <div class="preview-container">
    <div class="preview-header">
      <h1 class="preview-title"><%= @attachment.filename %></h1>
      <div class="preview-actions">
        <%= link_to '下载文件', doc_download_path(@attachment), :class => 'btn btn-secondary' %>
        <button onclick="closePreview()" class="btn btn-primary">关闭预览</button>
      </div>
    </div>
    
    <div class="file-info">
      <span><strong>文件大小:</strong> <%= number_to_human_size(@attachment.filesize) %></span>
      <span><strong>上传时间:</strong> <%= format_time(@attachment.created_on) %></span>
      <% if @attachment.author %>
        <span><strong>上传者:</strong> <%= @attachment.author.name %></span>
      <% end %>
    </div>
    
    <div class="preview-content">
      <% case get_file_type(@attachment.filename) %>
      <% when 'image' %>
        <!-- 图片预览 -->
        <img src="<%= doc_download_path(@attachment) %>" 
             alt="<%= @attachment.filename %>" 
             class="preview-image">
             
      <% when 'pdf' %>
        <!-- PDF预览 -->
        <iframe src="<%= doc_download_path(@attachment) %>" 
                class="preview-frame"
                title="PDF预览">
        </iframe>
        
      <% when 'word', 'excel', 'powerpoint' %>
        <!-- Office文档预览 -->
        <div class="preview-message">
          <h3>📄 Office文档预览</h3>
          <p>此文档类型需要下载后使用相应软件打开查看。</p>
          <p>或者您可以配置vue-office组件来实现在线预览。</p>
          <br>
          <%= link_to '下载文档', doc_download_path(@attachment), 
                      :class => 'btn btn-primary',
                      :style => 'font-size: 16px; padding: 12px 24px;' %>
          <br><br>
          <div style="text-align: left; max-width: 500px; margin: 0 auto;">
            <h4>配置在线预览的步骤：</h4>
            <ol>
              <li>运行下载脚本：<code>./download_assets.sh</code></li>
              <li>确保vue-office文件下载成功</li>
              <li>在插件配置中启用vue-office预览</li>
            </ol>
          </div>
        </div>
        
      <% else %>
        <!-- 不支持的文件类型 -->
        <div class="preview-message">
          <h3>❌ 不支持预览</h3>
          <p>此文件类型暂不支持在线预览。</p>
          <p>请下载文件后使用相应软件打开。</p>
          <br>
          <%= link_to '下载文件', doc_download_path(@attachment), 
                      :class => 'btn btn-primary',
                      :style => 'font-size: 16px; padding: 12px 24px;' %>
        </div>
      <% end %>
    </div>
  </div>

  <script>
    function closePreview() {
      if (window.opener) {
        window.close();
      } else {
        history.back();
      }
    }
    
    // 设置页面标题
    document.title = '文档预览 - <%= @attachment.filename %>';
    
    // 处理PDF加载错误
    const iframe = document.querySelector('iframe');
    if (iframe) {
      iframe.onerror = function() {
        this.style.display = 'none';
        const container = this.parentNode;
        container.innerHTML = `
          <div class="preview-message">
            <h3>📄 PDF预览</h3>
            <p>浏览器无法直接预览此PDF文件。</p>
            <p>请下载文件后使用PDF阅读器打开。</p>
            <br>
            <a href="<%= doc_download_path(@attachment) %>" class="btn btn-primary" style="font-size: 16px; padding: 12px 24px;">下载PDF</a>
          </div>
        `;
      };
    }
    
    // 处理图片加载错误
    const img = document.querySelector('.preview-image');
    if (img) {
      img.onerror = function() {
        this.style.display = 'none';
        const container = this.parentNode;
        container.innerHTML = `
          <div class="preview-message">
            <h3>🖼️ 图片预览</h3>
            <p>无法加载此图片文件。</p>
            <p>请下载文件后使用图片查看器打开。</p>
            <br>
            <a href="<%= doc_download_path(@attachment) %>" class="btn btn-primary" style="font-size: 16px; padding: 12px 24px;">下载图片</a>
          </div>
        `;
      };
    }
  </script>
</body>
</html>
