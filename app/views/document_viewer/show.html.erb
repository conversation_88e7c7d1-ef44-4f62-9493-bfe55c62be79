<% content_for :header_tags do %>
  <%= stylesheet_link_tag 'document_viewer', :plugin => 'redmine_document_viewer' %>
<% end %>

<div class="document-viewer-show">
  <h2>文档预览 - <%= @attachment.filename %></h2>
  
  <div class="document-info-panel">
    <div class="info-row">
      <label>文件名:</label>
      <span><%= @attachment.filename %></span>
    </div>
    
    <div class="info-row">
      <label>文件大小:</label>
      <span><%= number_to_human_size(@attachment.filesize) %></span>
    </div>
    
    <div class="info-row">
      <label>上传时间:</label>
      <span><%= format_time(@attachment.created_on) %></span>
    </div>
    
    <% if @attachment.author %>
      <div class="info-row">
        <label>上传者:</label>
        <span><%= link_to @attachment.author.name, user_path(@attachment.author) %></span>
      </div>
    <% end %>
    
    <% if @attachment.description.present? %>
      <div class="info-row">
        <label>描述:</label>
        <span><%= @attachment.description %></span>
      </div>
    <% end %>
  </div>
  
  <div class="document-actions">
    <%= link_to '在线预览', @preview_url, 
                :class => 'btn btn-primary',
                :target => '_blank' %>
    <%= link_to '下载文件', @download_url, 
                :class => 'btn btn-secondary' %>
    <% if @attachment.container %>
      <%= link_to '返回', url_for(@attachment.container), 
                  :class => 'btn btn-default' %>
    <% end %>
  </div>
</div>

<style>
.document-viewer-show {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
}

.document-info-panel {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 20px;
  margin: 20px 0;
}

.info-row {
  display: flex;
  margin-bottom: 10px;
}

.info-row label {
  font-weight: bold;
  width: 100px;
  margin-right: 10px;
}

.document-actions {
  text-align: center;
  margin-top: 30px;
}

.btn {
  display: inline-block;
  padding: 10px 20px;
  margin: 0 5px;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 500;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-default {
  background: #e9ecef;
  color: #495057;
}

.btn:hover {
  opacity: 0.9;
}
</style>
