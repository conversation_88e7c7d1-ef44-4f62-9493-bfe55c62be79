<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>文档预览 - <%= @attachment.filename %></title>
  
  <!-- 文档信息meta标签 -->
  <meta name="document-filename" content="<%= @attachment.filename %>">
  <meta name="document-filesize" content="<%= @attachment.filesize %>">
  <meta name="document-content-type" content="<%= @attachment.content_type %>">
  <meta name="document-download-url" content="<%= doc_download_path(@attachment) %>">
  <% if @attachment.author %>
    <meta name="document-author" content="<%= @attachment.author.name %>">
  <% end %>
  <meta name="document-created-on" content="<%= @attachment.created_on.iso8601 %>">
  
  <!-- 加载构建后的CSS -->
  <%= stylesheet_link_tag 'preview-viewer', :plugin => 'redmine_document_viewer' %>
  
  <style>
    /* 基础样式，确保在JS加载前页面不会空白 */
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f5f5f5;
    }
    
    #preview-app {
      height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .loading-placeholder {
      text-align: center;
      color: #666;
    }
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 20px;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <!-- Vue应用挂载点 -->
  <div id="preview-app">
    <div class="loading-placeholder">
      <div class="loading-spinner"></div>
      <p>正在加载文档预览...</p>
    </div>
  </div>

  <!-- 全局文档信息 -->
  <script>
    // 将文档信息暴露给Vue应用
    window.documentInfo = {
      attachmentId: '<%= @attachment.id %>',
      filename: '<%= j @attachment.filename %>',
      filesize: '<%= @attachment.filesize %>',
      contentType: '<%= @attachment.content_type %>',
      downloadUrl: '<%= doc_download_path(@attachment) %>',
      <% if @attachment.author %>
      author: '<%= j @attachment.author.name %>',
      <% end %>
      createdOn: '<%= @attachment.created_on.iso8601 %>'
    };
  </script>

  <!-- 加载构建后的JavaScript -->
  <%= javascript_include_tag 'preview-viewer', :plugin => 'redmine_document_viewer' %>
</body>
</html>
