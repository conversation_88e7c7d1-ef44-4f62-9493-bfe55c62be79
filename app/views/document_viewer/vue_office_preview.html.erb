<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>文档预览 - <%= @attachment.filename %></title>

  <!-- Vue.js -->
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>

  <!-- vue-office 组件 -->
  <script src="https://unpkg.com/@vue-office/docx@1.6.2/lib/index.umd.js"></script>
  <script src="https://unpkg.com/@vue-office/excel@1.7.11/lib/index.umd.js"></script>
  <script src="https://unpkg.com/@vue-office/pdf@2.0.2/lib/index.umd.js"></script>

  <!-- 样式 -->
  <link rel="stylesheet" href="https://unpkg.com/@vue-office/docx@1.6.2/lib/index.css">
  <link rel="stylesheet" href="https://unpkg.com/@vue-office/excel@1.7.11/lib/index.css">
  <link rel="stylesheet" href="https://unpkg.com/@vue-office/pdf@2.0.2/lib/index.css">

  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: Arial, sans-serif;
      background-color: #f5f5f5;
    }

    .preview-container {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .preview-header {
      background: #2c3e50;
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .preview-title {
      margin: 0;
      font-size: 18px;
    }

    .preview-actions {
      display: flex;
      gap: 10px;
    }

    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      text-decoration: none;
      font-size: 14px;
    }

    .btn-primary {
      background: #3498db;
      color: white;
    }

    .btn-secondary {
      background: #95a5a6;
      color: white;
    }

    .preview-content {
      height: calc(100vh - 120px);
      min-height: 600px;
    }

    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 400px;
      font-size: 16px;
      color: #666;
    }

    .error {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 400px;
      font-size: 16px;
      color: #e74c3c;
    }
  </style>
</head>
<body>
  <div id="app">
    <div class="preview-container">
      <div class="preview-header">
        <h1 class="preview-title">{{ filename }}</h1>
        <div class="preview-actions">
          <a :href="downloadUrl" class="btn btn-secondary">下载文件</a>
          <button @click="closePreview" class="btn btn-primary">关闭预览</button>
        </div>
      </div>

      <div class="preview-content">
        <!-- Word文档预览 -->
        <vue-office-docx
          v-if="fileType === 'word'"
          :src="fileUrl"
          style="height: 100%;"
          @rendered="onRendered"
          @error="onError"
        />

        <!-- Excel表格预览 -->
        <vue-office-excel
          v-if="fileType === 'excel'"
          :src="fileUrl"
          style="height: 100%;"
          @rendered="onRendered"
          @error="onError"
        />

        <!-- PDF预览 -->
        <vue-office-pdf
          v-if="fileType === 'pdf'"
          :src="fileUrl"
          style="height: 100%;"
          @rendered="onRendered"
          @error="onError"
        />

        <!-- 加载状态 -->
        <div v-if="loading" class="loading">
          正在加载文档...
        </div>

        <!-- 错误状态 -->
        <div v-if="error" class="error">
          {{ errorMessage }}
        </div>
      </div>
    </div>
  </div>

  <script>
    const { createApp } = Vue;

    createApp({
      components: {
        VueOfficeDocx: VueOfficeDocx.default,
        VueOfficeExcel: VueOfficeExcel.default,
        VueOfficePdf: VueOfficePdf.default
      },

      data() {
        return {
          filename: '<%= @attachment.filename %>',
          fileUrl: '<%= doc_download_url(@attachment) %>',
          downloadUrl: '<%= doc_download_url(@attachment) %>',
          fileType: '<%= get_file_type(@attachment.filename) %>',
          loading: true,
          error: false,
          errorMessage: ''
        }
      },

      methods: {
        onRendered() {
          this.loading = false;
          console.log('文档渲染完成');
        },

        onError(error) {
          this.loading = false;
          this.error = true;
          this.errorMessage = '文档加载失败: ' + (error.message || '未知错误');
          console.error('文档预览错误:', error);
        },

        closePreview() {
          if (window.opener) {
            window.close();
          } else {
            history.back();
          }
        }
      },

      mounted() {
        // 设置页面标题
        document.title = `文档预览 - ${this.filename}`;
      }
    }).mount('#app');
  </script>
</body>
</html>
