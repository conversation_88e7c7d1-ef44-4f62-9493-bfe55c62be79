<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>文档预览 - <%= @attachment.filename %></title>

  <!-- Vue.js 本地版本 -->
  <%= javascript_include_tag 'vue.global.js', :plugin => 'redmine_document_viewer' %>

  <!-- 确保Vue在全局作用域中正确暴露 -->
  <script>
    // 确保Vue及其所有方法在全局作用域中可用
    if (typeof Vue !== 'undefined') {
      // 为vue-office组件提供Vue的全局引用
      window.Vue = Vue;

      // 确保defineComponent等方法可用
      if (Vue.defineComponent) {
        window.defineComponent = Vue.defineComponent;
      }

      // 为UMD模块提供Vue引用
      if (typeof global !== 'undefined') {
        global.Vue = Vue;
      }

      console.log('Vue版本:', Vue.version);
      console.log('Vue.defineComponent可用:', typeof Vue.defineComponent);
    } else {
      console.error('Vue未正确加载');
    }
  </script>

  <!-- vue-office 组件本地版本 (延迟加载确保Vue已准备好) -->
  <script>
    // 延迟加载vue-office组件，确保Vue已完全初始化
    function loadVueOfficeComponents() {
      const scripts = [
        '<%= asset_path('vue-office-docx.min.js', :plugin => 'redmine_document_viewer') %>',
        '<%= asset_path('vue-office-excel.umd.min.js', :plugin => 'redmine_document_viewer') %>',
        '<%= asset_path('vue-office-pdf.umd.min.js', :plugin => 'redmine_document_viewer') %>'
      ];

      let loadedCount = 0;

      scripts.forEach(src => {
        const script = document.createElement('script');
        script.src = src;
        script.onload = () => {
          loadedCount++;
          if (loadedCount === scripts.length) {
            console.log('所有vue-office组件已加载');
            // 触发自定义事件表示组件已准备好
            window.dispatchEvent(new Event('vueOfficeReady'));
          }
        };
        script.onerror = () => {
          console.error('加载失败:', src);
        };
        document.head.appendChild(script);
      });
    }

    // 确保Vue完全加载后再加载vue-office组件
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', loadVueOfficeComponents);
    } else {
      loadVueOfficeComponents();
    }
  </script>

  <!-- 样式本地版本 -->
  <%= stylesheet_link_tag 'vue-office-docx.min.css', :plugin => 'redmine_document_viewer' %>
  <%= stylesheet_link_tag 'vue-office-excel.min.css', :plugin => 'redmine_document_viewer' %>

  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: Arial, sans-serif;
      background-color: #f5f5f5;
    }

    .preview-container {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .preview-header {
      background: #2c3e50;
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .preview-title {
      margin: 0;
      font-size: 18px;
    }

    .preview-actions {
      display: flex;
      gap: 10px;
    }

    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      text-decoration: none;
      font-size: 14px;
    }

    .btn-primary {
      background: #3498db;
      color: white;
    }

    .btn-secondary {
      background: #95a5a6;
      color: white;
    }

    .preview-content {
      height: calc(100vh - 120px);
      min-height: 600px;
    }

    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 400px;
      font-size: 16px;
      color: #666;
    }

    .error {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 400px;
      font-size: 16px;
      color: #e74c3c;
    }
  </style>
</head>
<body>
  <div id="app">
    <div class="preview-container">
      <div class="preview-header">
        <h1 class="preview-title">{{ filename }}</h1>
        <div class="preview-actions">
          <a :href="downloadUrl" class="btn btn-secondary">下载文件</a>
          <button @click="closePreview" class="btn btn-primary">关闭预览</button>
        </div>
      </div>

      <div class="preview-content">
        <!-- Word文档预览 -->
        <vue-office-docx
          v-if="fileType === 'word'"
          :src="fileUrl"
          style="height: 100%;"
          @rendered="onRendered"
          @error="onError"
        />

        <!-- Excel表格预览 -->
        <vue-office-excel
          v-if="fileType === 'excel'"
          :src="fileUrl"
          style="height: 100%;"
          @rendered="onRendered"
          @error="onError"
        />

        <!-- PDF预览 -->
        <vue-office-pdf
          v-if="fileType === 'pdf'"
          :src="fileUrl"
          style="height: 100%;"
          @rendered="onRendered"
          @error="onError"
        />

        <!-- PowerPoint预览 -->
        <vue-office-pptx
          v-if="fileType === 'powerpoint'"
          :src="fileUrl"
          style="height: 100%;"
          @rendered="onRendered"
          @error="onError"
        />

        <!-- 加载状态 -->
        <div v-if="loading" class="loading">
          正在加载文档...
        </div>

        <!-- 错误状态 -->
        <div v-if="error" class="error">
          {{ errorMessage }}
        </div>
      </div>
    </div>
  </div>

  <script>
    // 初始化Vue应用
    function initVueApp() {
      // 检查Vue是否可用
      if (typeof Vue === 'undefined') {
        console.error('Vue未加载');
        document.getElementById('app').innerHTML = '<div class="error">Vue.js未正确加载，请检查资源文件</div>';
        return;
      }

      console.log('开始初始化Vue应用...');

      const { createApp } = Vue;

      // 检查vue-office组件是否可用
      const components = {};

      if (typeof VueOfficeDocx !== 'undefined') {
        components.VueOfficeDocx = VueOfficeDocx.default || VueOfficeDocx;
        console.log('VueOfficeDocx组件已注册');
      } else {
        console.warn('VueOfficeDocx组件未找到');
      }

      if (typeof VueOfficeExcel !== 'undefined') {
        components.VueOfficeExcel = VueOfficeExcel.default || VueOfficeExcel;
        console.log('VueOfficeExcel组件已注册');
      } else {
        console.warn('VueOfficeExcel组件未找到');
      }

      if (typeof VueOfficePdf !== 'undefined') {
        components.VueOfficePdf = VueOfficePdf.default || VueOfficePdf;
        console.log('VueOfficePdf组件已注册');
      } else {
        console.warn('VueOfficePdf组件未找到');
      }

      if (typeof VueOfficePptx !== 'undefined') {
        components.VueOfficePptx = VueOfficePptx.default || VueOfficePptx;
        console.log('VueOfficePptx组件已注册');
      } else {
        console.warn('VueOfficePptx组件未找到');
      }

      try {
        const app = createApp({
          components: components,

      data() {
        return {
          filename: '<%= @attachment.filename %>',
          fileUrl: '<%= doc_download_url(@attachment) %>',
          downloadUrl: '<%= doc_download_url(@attachment) %>',
          fileType: '<%= get_file_type(@attachment.filename) %>',
          loading: true,
          error: false,
          errorMessage: ''
        }
      },

      methods: {
        onRendered() {
          this.loading = false;
          console.log('文档渲染完成');
        },

        onError(error) {
          this.loading = false;
          this.error = true;
          this.errorMessage = '文档加载失败: ' + (error.message || '未知错误');
          console.error('文档预览错误:', error);
        },

        closePreview() {
          if (window.opener) {
            window.close();
          } else {
            history.back();
          }
        }
      },

      mounted() {
          // 设置页面标题
          document.title = `文档预览 - ${this.filename}`;
        }
      });

      app.mount('#app');
      console.log('Vue应用初始化成功');

      } catch (error) {
        console.error('Vue应用初始化失败:', error);
        document.getElementById('app').innerHTML =
          '<div class="error">Vue应用初始化失败: ' + error.message + '</div>';
      }
    }

    // 等待vue-office组件加载完成后初始化Vue应用
    window.addEventListener('vueOfficeReady', function() {
      console.log('vue-office组件已准备好，开始初始化Vue应用');
      initVueApp();
    });

    // 如果页面已经加载完成，直接初始化（降级处理）
    document.addEventListener('DOMContentLoaded', function() {
      // 等待一段时间，如果vue-office组件还没加载完成，则直接初始化
      setTimeout(function() {
        if (!document.querySelector('#app').__vue__) {
          console.log('vue-office组件加载超时，使用降级模式');
          initVueApp();
        }
      }, 3000);
    });
  </script>
</body>
</html>
