<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>文档预览 - <%= @attachment.filename %></title>

  <!-- Vue.js 本地版本 -->
  <%= javascript_include_tag 'vue.global.js', :plugin => 'redmine_document_viewer' %>

  <!-- vue-office 组件本地版本 -->
  <%= javascript_include_tag 'vue-office-docx.umd.js', :plugin => 'redmine_document_viewer' %>
  <%= javascript_include_tag 'vue-office-excel.umd.js', :plugin => 'redmine_document_viewer' %>
  <%= javascript_include_tag 'vue-office-pdf.umd.js', :plugin => 'redmine_document_viewer' %>

  <!-- 样式本地版本 -->
  <%= stylesheet_link_tag 'vue-office-docx.css', :plugin => 'redmine_document_viewer' %>
  <%= stylesheet_link_tag 'vue-office-excel.css', :plugin => 'redmine_document_viewer' %>
  <%= stylesheet_link_tag 'vue-office-pdf.css', :plugin => 'redmine_document_viewer' %>

  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: Arial, sans-serif;
      background-color: #f5f5f5;
    }

    .preview-container {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .preview-header {
      background: #2c3e50;
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .preview-title {
      margin: 0;
      font-size: 18px;
    }

    .preview-actions {
      display: flex;
      gap: 10px;
    }

    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      text-decoration: none;
      font-size: 14px;
    }

    .btn-primary {
      background: #3498db;
      color: white;
    }

    .btn-secondary {
      background: #95a5a6;
      color: white;
    }

    .preview-content {
      height: calc(100vh - 120px);
      min-height: 600px;
    }

    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 400px;
      font-size: 16px;
      color: #666;
    }

    .error {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 400px;
      font-size: 16px;
      color: #e74c3c;
    }
  </style>
</head>
<body>
  <div id="app">
    <div class="preview-container">
      <div class="preview-header">
        <h1 class="preview-title">{{ filename }}</h1>
        <div class="preview-actions">
          <a :href="downloadUrl" class="btn btn-secondary">下载文件</a>
          <button @click="closePreview" class="btn btn-primary">关闭预览</button>
        </div>
      </div>

      <div class="preview-content">
        <!-- Word文档预览 -->
        <vue-office-docx
          v-if="fileType === 'word'"
          :src="fileUrl"
          style="height: 100%;"
          @rendered="onRendered"
          @error="onError"
        />

        <!-- Excel表格预览 -->
        <vue-office-excel
          v-if="fileType === 'excel'"
          :src="fileUrl"
          style="height: 100%;"
          @rendered="onRendered"
          @error="onError"
        />

        <!-- PDF预览 -->
        <vue-office-pdf
          v-if="fileType === 'pdf'"
          :src="fileUrl"
          style="height: 100%;"
          @rendered="onRendered"
          @error="onError"
        />

        <!-- 加载状态 -->
        <div v-if="loading" class="loading">
          正在加载文档...
        </div>

        <!-- 错误状态 -->
        <div v-if="error" class="error">
          {{ errorMessage }}
        </div>
      </div>
    </div>
  </div>

  <script>
    const { createApp } = Vue;

    createApp({
      components: {
        VueOfficeDocx: VueOfficeDocx.default,
        VueOfficeExcel: VueOfficeExcel.default,
        VueOfficePdf: VueOfficePdf.default
      },

      data() {
        return {
          filename: '<%= @attachment.filename %>',
          fileUrl: '<%= doc_download_url(@attachment) %>',
          downloadUrl: '<%= doc_download_url(@attachment) %>',
          fileType: '<%= get_file_type(@attachment.filename) %>',
          loading: true,
          error: false,
          errorMessage: ''
        }
      },

      methods: {
        onRendered() {
          this.loading = false;
          console.log('文档渲染完成');
        },

        onError(error) {
          this.loading = false;
          this.error = true;
          this.errorMessage = '文档加载失败: ' + (error.message || '未知错误');
          console.error('文档预览错误:', error);
        },

        closePreview() {
          if (window.opener) {
            window.close();
          } else {
            history.back();
          }
        }
      },

      mounted() {
        // 设置页面标题
        document.title = `文档预览 - ${this.filename}`;
      }
    }).mount('#app');
  </script>
</body>
</html>
