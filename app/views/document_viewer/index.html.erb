<% content_for :header_tags do %>
  <%= stylesheet_link_tag 'document_viewer', :plugin => 'redmine_document_viewer' %>
  <%= javascript_include_tag 'document_viewer', :plugin => 'redmine_document_viewer' %>
<% end %>

<div class="contextual">
  <%= link_to l(:label_document_new), new_project_document_path(@project), 
              :class => 'icon icon-add' if User.current.allowed_to?(:manage_documents, @project) %>
</div>

<h2>文档查看器</h2>

<% if @attachments.any? %>
  <div class="document-viewer-container">
    <div class="document-filters">
      <label>文件类型筛选:</label>
      <select id="file-type-filter">
        <option value="">全部</option>
        <option value="image">图片</option>
        <option value="pdf">PDF</option>
        <option value="word">Word文档</option>
        <option value="excel">Excel表格</option>
        <option value="powerpoint">PowerPoint演示</option>
      </select>
    </div>

    <div class="document-grid">
      <% @attachments.each do |attachment| %>
        <div class="document-card" data-file-type="<%= get_file_type(attachment.filename) %>">
          <div class="document-thumbnail">
            <%= link_to document_viewer_path(attachment), :class => 'document-link' do %>
              <div class="file-icon <%= get_file_icon_class(attachment.filename) %>">
                <%= get_file_icon(attachment.filename) %>
              </div>
            <% end %>
          </div>
          
          <div class="document-info">
            <h4 class="document-title">
              <%= link_to truncate(attachment.filename, :length => 30), 
                          document_viewer_path(attachment) %>
            </h4>
            
            <div class="document-meta">
              <span class="file-size"><%= number_to_human_size(attachment.filesize) %></span>
              <span class="upload-date"><%= format_time(attachment.created_on) %></span>
            </div>
            
            <div class="document-container-info">
              <% if attachment.container %>
                <%= link_to attachment.container.to_s, 
                            url_for(attachment.container), :class => 'container-link' %>
              <% end %>
            </div>
            
            <div class="document-actions">
              <%= link_to '预览', preview_document_viewer_path(attachment), 
                          :class => 'btn btn-sm btn-primary preview-btn',
                          :target => '_blank' %>
              <%= link_to '下载', download_document_viewer_path(attachment), 
                          :class => 'btn btn-sm btn-secondary' %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
<% else %>
  <p class="nodata">该项目暂无可预览的文档。</p>
<% end %>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // 文件类型筛选功能
  const filterSelect = document.getElementById('file-type-filter');
  const documentCards = document.querySelectorAll('.document-card');
  
  filterSelect.addEventListener('change', function() {
    const selectedType = this.value;
    
    documentCards.forEach(function(card) {
      if (selectedType === '' || card.dataset.fileType === selectedType) {
        card.style.display = 'block';
      } else {
        card.style.display = 'none';
      }
    });
  });
});
</script>
