<%# 覆盖Redmine默认的附件链接视图，添加预览功能 %>
<% if defined?(container) && container %>
  <% container.attachments.each do |attachment| %>
    <p>
      <%= link_to_attachment attachment, :class => 'icon icon-attachment' -%>

      <%# 添加预览按钮 %>
      <% if attachment.previewable? %>
        <%= link_to '预览',
                    doc_preview_path(attachment),
                    :class => 'icon icon-preview document-preview-btn',
                    :target => '_blank',
                    :title => "预览 #{attachment.filename}",
                    :style => 'margin-left: 10px;' %>
      <% end %>

      <% if attachment.is_text? %>
        <%= link_to '查看',
                    :controller => 'attachments', :action => 'show',
                    :id => attachment, :filename => attachment.filename,
                    :class => 'icon icon-magnifier',
                    :style => 'margin-left: 5px;' %>
      <% end %>

      <% if attachment.editable? %>
        <%= link_to '编辑',
                    :controller => 'attachments', :action => 'edit', :id => attachment,
                    :class => 'icon icon-edit',
                    :style => 'margin-left: 5px;' %>
      <% end %>

      <% if User.current.allowed_to?(:manage_files, attachment.project) %>
        <%= link_to '删除',
                    attachment_path(attachment),
                    :method => :delete,
                    :data => {:confirm => l(:text_are_you_sure)},
                    :class => 'icon icon-del',
                    :style => 'margin-left: 5px;' %>
      <% end %>

      <span class="size">(<%= number_to_human_size attachment.filesize %>)</span>
      <% if attachment.description.present? %>
        <div class="wiki"><%= textilizable attachment.description %></div>
      <% end %>
    </p>
  <% end %>
<% end %>
