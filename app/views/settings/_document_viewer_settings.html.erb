<div class="box tabular settings">
  <h3>文档查看器设置</h3>

  <fieldset>
    <legend>支持的文件类型</legend>

    <p>
      <label>
        <%= check_box_tag 'settings[enable_word_preview]', true,
                          @settings['enable_word_preview'], :id => 'enable_word_preview' %>
        启用Word文档预览 (.docx, .doc, .rtf)
      </label>
    </p>

    <p>
      <label>
        <%= check_box_tag 'settings[enable_excel_preview]', true,
                          @settings['enable_excel_preview'], :id => 'enable_excel_preview' %>
        启用Excel表格预览 (.xlsx, .xls, .csv)
      </label>
    </p>

    <p>
      <label>
        <%= check_box_tag 'settings[enable_pdf_preview]', true,
                          @settings['enable_pdf_preview'], :id => 'enable_pdf_preview' %>
        启用PDF文档预览 (.pdf)
      </label>
    </p>

    <p>
      <label>
        <%= check_box_tag 'settings[enable_image_preview]', true,
                          @settings['enable_image_preview'], :id => 'enable_image_preview' %>
        启用图片预览 (.jpg, .png, .gif, .bmp, .webp)
      </label>
    </p>
  </fieldset>

  <fieldset>
    <legend>预览方法配置</legend>

    <p>
      <label for="preview_method">预览方法:</label>
      <%= select_tag 'settings[preview_method]',
                     options_for_select([
                       ['Vue Office (推荐)', 'vue_office'],
                       ['备用预览 (兼容性最好)', 'fallback'],
                       ['kkFileView', 'kkfileview'],
                       ['OnlyOffice', 'onlyoffice']
                     ], @settings['preview_method']),
                     :id => 'preview_method' %>
      <em>选择文档预览的实现方式。如果Vue Office出现问题，建议选择备用预览</em>
    </p>

    <div id="kkfileview_settings" style="<%= 'display: none;' unless @settings['preview_method'] == 'kkfileview' %>">
      <p>
        <label for="kkfileview_url">kkFileView服务地址:</label>
        <%= text_field_tag 'settings[kkfileview_url]', @settings['kkfileview_url'],
                           :placeholder => 'http://localhost:8012', :size => 50 %>
        <em>kkFileView服务的完整URL地址</em>
      </p>
    </div>

    <div id="onlyoffice_settings" style="<%= 'display: none;' unless @settings['preview_method'] == 'onlyoffice' %>">
      <p>
        <label for="onlyoffice_url">OnlyOffice服务地址:</label>
        <%= text_field_tag 'settings[onlyoffice_url]', @settings['onlyoffice_url'],
                           :placeholder => 'http://localhost:8080', :size => 50 %>
        <em>OnlyOffice Document Server的完整URL地址</em>
      </p>

      <p>
        <label for="onlyoffice_secret">OnlyOffice密钥:</label>
        <%= password_field_tag 'settings[onlyoffice_secret]', @settings['onlyoffice_secret'],
                               :size => 50 %>
        <em>OnlyOffice Document Server的JWT密钥（可选）</em>
      </p>
    </div>
  </fieldset>

  <fieldset>
    <legend>文件限制</legend>

    <p>
      <label for="max_file_size">最大文件大小 (MB):</label>
      <%= number_field_tag 'settings[max_file_size]', @settings['max_file_size'] || 50,
                           :min => 1, :max => 500, :step => 1 %>
      <em>允许预览的最大文件大小，单位为MB</em>
    </p>

    <p>
      <label>
        <%= check_box_tag 'settings[enable_cache]', true,
                          @settings['enable_cache'], :id => 'enable_cache' %>
        启用预览缓存
      </label>
      <em>缓存转换后的预览文件以提高性能</em>
    </p>

    <p>
      <label for="cache_duration">缓存时间 (小时):</label>
      <%= number_field_tag 'settings[cache_duration]', @settings['cache_duration'] || 24,
                           :min => 1, :max => 168, :step => 1 %>
      <em>预览文件的缓存时间，单位为小时</em>
    </p>
  </fieldset>

  <fieldset>
    <legend>安全设置</legend>

    <p>
      <label>
        <%= check_box_tag 'settings[require_permission]', true,
                          @settings['require_permission'], :id => 'require_permission' %>
        需要查看权限
      </label>
      <em>用户需要有相应的权限才能预览文档</em>
    </p>

    <p>
      <label>
        <%= check_box_tag 'settings[watermark_enabled]', true,
                          @settings['watermark_enabled'], :id => 'watermark_enabled' %>
        启用水印
      </label>
      <em>在预览文档上添加用户信息水印</em>
    </p>

    <div id="watermark_settings" style="<%= 'display: none;' unless @settings['watermark_enabled'] %>">
      <p>
        <label for="watermark_text">水印文本:</label>
        <%= text_field_tag 'settings[watermark_text]', @settings['watermark_text'],
                           :placeholder => '{{user}} - {{date}}', :size => 50 %>
        <em>支持变量: {{user}}, {{date}}, {{project}}</em>
      </p>
    </div>
  </fieldset>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // 预览方法切换
  const previewMethodSelect = document.getElementById('preview_method');
  const kkfileviewSettings = document.getElementById('kkfileview_settings');
  const onlyofficeSettings = document.getElementById('onlyoffice_settings');

  function togglePreviewMethodSettings() {
    const method = previewMethodSelect.value;

    kkfileviewSettings.style.display = (method === 'kkfileview') ? 'block' : 'none';
    onlyofficeSettings.style.display = (method === 'onlyoffice') ? 'block' : 'none';
  }

  previewMethodSelect.addEventListener('change', togglePreviewMethodSettings);

  // 水印设置切换
  const watermarkEnabled = document.getElementById('watermark_enabled');
  const watermarkSettings = document.getElementById('watermark_settings');

  function toggleWatermarkSettings() {
    watermarkSettings.style.display = watermarkEnabled.checked ? 'block' : 'none';
  }

  watermarkEnabled.addEventListener('change', toggleWatermarkSettings);
});
</script>
