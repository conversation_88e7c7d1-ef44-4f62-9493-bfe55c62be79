class DocumentViewerController < ApplicationController
  before_action :find_attachment, :only => [:show, :preview, :download]
  before_action :check_preview_permission, :only => [:preview, :show]

  def show
    # 显示单个文档的详细信息和预览
    @preview_url = preview_document_viewer_path(@attachment)
    @download_url = download_document_viewer_path(@attachment)
  end

  def preview
    # 根据文件类型返回预览内容
    case @attachment.filename.downcase
    when /\.(jpg|jpeg|png|gif|bmp|webp)$/i
      preview_image
    when /\.(pdf)$/i
      preview_pdf
    when /\.(docx?|rtf)$/i
      preview_word
    when /\.(xlsx?|csv)$/i
      preview_excel
    when /\.(pptx?|ppt)$/i
      preview_powerpoint
    else
      render :json => { :error => '不支持的文件格式' }, :status => 422
    end
  end

  def download
    # 下载原始文件
    if @attachment.readable?
      send_file @attachment.diskfile,
                :filename => filename_for_content_disposition(@attachment.filename),
                :type => @attachment.content_type,
                :disposition => 'attachment'
    else
      render_404
    end
  end

  private

  def find_attachment
    @attachment = Attachment.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    render_404
  end

  def check_preview_permission
    # 检查用户是否有预览权限
    unless @attachment.visible?(User.current)
      render_403
      return false
    end

    # 检查文件是否支持预览
    unless @attachment.previewable?
      render_404
      return false
    end

    true
  end

  def preview_image
    # 图片预览
    send_file @attachment.diskfile,
              :type => @attachment.content_type,
              :disposition => 'inline'
  end

  def preview_pdf
    # PDF预览 - 直接返回PDF文件，浏览器会处理
    send_file @attachment.diskfile,
              :type => 'application/pdf',
              :disposition => 'inline'
  end

  def preview_word
    # Word文档预览 - 使用vue-office或转换为HTML
    case Setting.plugin_redmine_document_viewer['preview_method']
    when 'vue_office'
      render :template => 'document_viewer/vue_office_preview'
    when 'kkfileview'
      redirect_to kkfileview_url(@attachment)
    else
      render :json => { :error => 'Word预览功能未配置' }, :status => 422
    end
  end

  def preview_excel
    # Excel预览
    case Setting.plugin_redmine_document_viewer['preview_method']
    when 'vue_office'
      render :template => 'document_viewer/vue_office_preview'
    when 'kkfileview'
      redirect_to kkfileview_url(@attachment)
    else
      render :json => { :error => 'Excel预览功能未配置' }, :status => 422
    end
  end

  def preview_powerpoint
    # PowerPoint预览
    case Setting.plugin_redmine_document_viewer['preview_method']
    when 'vue_office'
      render :template => 'document_viewer/vue_office_preview'
    when 'kkfileview'
      redirect_to kkfileview_url(@attachment)
    else
      render :json => { :error => 'PowerPoint预览功能未配置' }, :status => 422
    end
  end

  def supported_file_extensions_regex
    # 支持的文件扩展名正则表达式
    '\.(jpg|jpeg|png|gif|bmp|webp|pdf|docx?|xlsx?|pptx?|csv|rtf)$'
  end

  def kkfileview_url(attachment)
    # 构建kkFileView预览URL
    base_url = Setting.plugin_redmine_document_viewer['kkfileview_url'] || 'http://localhost:8012'
    file_url = url_for(:controller => 'attachments', :action => 'download',
                      :id => attachment, :filename => attachment.filename, :only_path => false)
    "#{base_url}/onlinePreview?url=#{CGI.escape(file_url)}"
  end
end
