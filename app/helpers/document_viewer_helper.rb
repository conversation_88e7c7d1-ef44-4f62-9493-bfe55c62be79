module DocumentViewerHelper
  
  # 获取文件类型
  def get_file_type(filename)
    case filename.downcase
    when /\.(jpg|jpeg|png|gif|bmp|webp)$/i
      'image'
    when /\.(pdf)$/i
      'pdf'
    when /\.(docx?|rtf)$/i
      'word'
    when /\.(xlsx?|csv)$/i
      'excel'
    when /\.(pptx?|ppt)$/i
      'powerpoint'
    else
      'unknown'
    end
  end
  
  # 获取文件图标CSS类
  def get_file_icon_class(filename)
    case get_file_type(filename)
    when 'image'
      'icon-image'
    when 'pdf'
      'icon-pdf'
    when 'word'
      'icon-word'
    when 'excel'
      'icon-excel'
    when 'powerpoint'
      'icon-powerpoint'
    else
      'icon-file'
    end
  end
  
  # 获取文件图标HTML
  def get_file_icon(filename)
    case get_file_type(filename)
    when 'image'
      '🖼️'
    when 'pdf'
      '📄'
    when 'word'
      '📝'
    when 'excel'
      '📊'
    when 'powerpoint'
      '📽️'
    else
      '📁'
    end
  end
  
  # 检查文件是否支持预览
  def file_previewable?(filename)
    supported_extensions = %w[jpg jpeg png gif bmp webp pdf docx doc xlsx xls pptx ppt csv rtf]
    extension = File.extname(filename).downcase.gsub('.', '')
    supported_extensions.include?(extension)
  end
  
  # 获取预览方法配置
  def preview_method_enabled?(method)
    Setting.plugin_redmine_document_viewer[method] == true
  end
  
  # 格式化文件大小
  def format_file_size(size)
    number_to_human_size(size)
  end
  
  # 获取文件扩展名
  def get_file_extension(filename)
    File.extname(filename).downcase.gsub('.', '')
  end
  
  # 检查文件大小是否在允许范围内
  def file_size_allowed?(filesize)
    max_size = (Setting.plugin_redmine_document_viewer['max_file_size'] || 50).to_i
    filesize <= max_size.megabytes
  end
  
  # 生成预览URL
  def generate_preview_url(attachment, method = nil)
    method ||= Setting.plugin_redmine_document_viewer['preview_method'] || 'vue_office'
    
    case method
    when 'vue_office'
      preview_document_viewer_path(attachment)
    when 'kkfileview'
      generate_kkfileview_url(attachment)
    when 'onlyoffice'
      generate_onlyoffice_url(attachment)
    else
      preview_document_viewer_path(attachment)
    end
  end
  
  # 生成kkFileView预览URL
  def generate_kkfileview_url(attachment)
    base_url = Setting.plugin_redmine_document_viewer['kkfileview_url'] || 'http://localhost:8012'
    file_url = url_for(:controller => 'attachments', :action => 'download', 
                      :id => attachment, :filename => attachment.filename, :only_path => false)
    "#{base_url}/onlinePreview?url=#{CGI.escape(file_url)}"
  end
  
  # 生成OnlyOffice预览URL
  def generate_onlyoffice_url(attachment)
    base_url = Setting.plugin_redmine_document_viewer['onlyoffice_url'] || 'http://localhost:8080'
    file_url = url_for(:controller => 'attachments', :action => 'download', 
                      :id => attachment, :filename => attachment.filename, :only_path => false)
    "#{base_url}/editor?url=#{CGI.escape(file_url)}"
  end
  
  # 获取支持的文件类型列表
  def supported_file_types
    {
      'image' => %w[jpg jpeg png gif bmp webp],
      'pdf' => %w[pdf],
      'word' => %w[docx doc rtf],
      'excel' => %w[xlsx xls csv],
      'powerpoint' => %w[pptx ppt]
    }
  end
  
  # 检查预览方法是否可用
  def preview_method_available?(method)
    case method
    when 'vue_office'
      true # 总是可用，因为是前端库
    when 'kkfileview'
      # 检查kkFileView服务是否可用
      check_service_availability(Setting.plugin_redmine_document_viewer['kkfileview_url'])
    when 'onlyoffice'
      # 检查OnlyOffice服务是否可用
      check_service_availability(Setting.plugin_redmine_document_viewer['onlyoffice_url'])
    else
      false
    end
  end
  
  private
  
  # 检查服务可用性
  def check_service_availability(url)
    return false if url.blank?
    
    begin
      uri = URI.parse(url)
      http = Net::HTTP.new(uri.host, uri.port)
      http.use_ssl = (uri.scheme == 'https')
      http.open_timeout = 5
      http.read_timeout = 5
      
      response = http.head('/')
      response.code.to_i < 400
    rescue
      false
    end
  end
end
