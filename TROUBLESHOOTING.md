# 故障排除指南

## 常见JavaScript错误及解决方案

### 1. MutationObserver错误

**错误信息**：
```
Uncaught TypeError: Failed to execute 'observe' on 'MutationObserver': parameter 1 is not of type 'Node'.
```

**原因**：document.body在脚本执行时还未准备好

**解决方案**：
已在 `document_viewer.js` 中修复，确保DOM加载完成后再初始化MutationObserver

### 2. Vue组件未定义错误

**错误信息**：
```
Uncaught TypeError: Cannot read properties of undefined (reading 'defineComponent')
```

**原因**：vue-office组件无法找到Vue实例

**解决方案**：

1. **检查Vue是否正确加载**：
```bash
# 检查Vue文件是否存在且大小正常
ls -lh assets/javascripts/vue.global.js
```

2. **确保Vue在全局作用域中**：
已在预览页面中添加全局Vue检查

3. **验证文件完整性**：
```bash
./check_assets.sh
```

### 3. VueOfficeDocx未定义错误

**错误信息**：
```
Uncaught ReferenceError: VueOfficeDocx is not defined
```

**原因**：vue-office组件文件未正确加载或损坏

**解决方案**：

1. **重新下载组件文件**：
```bash
./copy_from_cdn.sh
# 或
./update_assets.sh
```

2. **检查文件大小**：
```bash
ls -lh assets/javascripts/vue-office-*.js
```

3. **验证文件内容**：
```bash
head -n 5 assets/javascripts/vue-office-docx.min.js
```

4. **使用备用预览模式**：
如果vue-office组件持续出现问题，插件会自动切换到备用预览模式：
- 图片：直接显示
- PDF：浏览器内置预览
- Office文档：尝试使用Microsoft Office Online Viewer

5. **强制使用备用模式**：
在插件配置中选择预览方法为"简化预览"

## 资源文件问题

### 1. 文件下载失败

**症状**：文件存在但大小很小（几十字节）

**原因**：下载的是重定向页面而不是实际文件

**解决方案**：

1. **使用本地CDN复制**：
```bash
./copy_from_cdn.sh
```

2. **手动下载文件**：
参考 `MANUAL_DOWNLOAD.md`

3. **检查网络连接**：
```bash
curl -I https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.js
```

### 2. 文件权限问题

**症状**：文件存在但无法加载

**解决方案**：
```bash
# 设置正确的文件权限
chmod 644 assets/javascripts/*.js
chmod 644 assets/stylesheets/*.css
chown -R redmine:redmine assets/
```

### 3. 缓存问题

**症状**：更新文件后仍然报错

**解决方案**：
```bash
# 清除Redmine缓存
rm -rf tmp/cache/*

# 重启Web服务器
sudo systemctl restart apache2

# 清除浏览器缓存
# Ctrl+F5 或 Cmd+Shift+R
```

## 预览功能问题

### 1. 预览按钮不显示

**检查清单**：
- [ ] 插件是否正确安装
- [ ] 文件格式是否支持
- [ ] 用户是否有权限
- [ ] JavaScript是否正确加载

**解决步骤**：

1. **检查插件状态**：
```bash
ls -la /path/to/redmine/plugins/redmine_document_viewer
```

2. **检查浏览器控制台**：
按F12查看是否有JavaScript错误

3. **测试JavaScript功能**：
打开 `test_vue_office.html` 检查组件加载状态

### 2. 预览页面空白

**可能原因**：
- Vue或vue-office组件未正确加载
- 文件路径错误
- 权限问题

**解决步骤**：

1. **检查浏览器控制台错误**
2. **验证资源文件**：
```bash
./check_assets.sh
```
3. **测试简化预览**：
如果vue-office失败，插件会自动降级到简化预览模式

### 3. 特定文件格式无法预览

**Word/Excel/PowerPoint文档**：
- 确保vue-office组件正确加载
- 检查文件是否损坏
- 尝试重新上传文件

**PDF文档**：
- 检查浏览器是否支持PDF预览
- 尝试在新标签页中直接打开PDF

**图片文件**：
- 检查图片格式是否支持
- 验证文件权限

## 调试工具

### 1. 组件加载测试

打开 `test_vue_office.html` 检查：
- Vue.js是否正确加载
- vue-office组件是否可用
- 文件是否存在

### 2. 资源文件检查

运行检查脚本：
```bash
./check_assets.sh
```

### 3. 网络连接测试

测试CDN连接：
```bash
curl -I https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.js
```

### 4. Redmine日志

查看Redmine日志：
```bash
tail -f log/production.log
```

## 性能优化

### 1. 启用压缩

在Web服务器中启用gzip压缩：

**Apache**：
```apache
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/javascript
</IfModule>
```

**Nginx**：
```nginx
gzip on;
gzip_types text/css application/javascript;
```

### 2. 设置缓存头

**Apache**：
```apache
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
</IfModule>
```

**Nginx**：
```nginx
location ~* \.(js|css)$ {
    expires 1M;
    add_header Cache-Control "public, immutable";
}
```

## 获取帮助

如果问题仍然存在：

1. **收集信息**：
   - 浏览器控制台错误截图
   - Redmine版本信息
   - 插件版本信息
   - 服务器环境信息

2. **检查文档**：
   - `README.md` - 基本说明
   - `LOCAL_ASSETS.md` - 资源配置
   - `MANUAL_DOWNLOAD.md` - 手动下载指南

3. **提交Issue**：
   在项目仓库中提交详细的问题报告

## 常用命令汇总

```bash
# 完整重新安装
./copy_from_cdn.sh
./check_assets.sh
sudo systemctl restart apache2

# 检查文件状态
ls -lh assets/javascripts/
ls -lh assets/stylesheets/

# 查看日志
tail -f log/production.log

# 清除缓存
rm -rf tmp/cache/*
```
