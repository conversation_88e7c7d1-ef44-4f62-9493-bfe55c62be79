{"name": "@vue-office/pptx", "version": "1.0.1", "description": "", "main": "lib/index.js", "files": ["lib/"], "scripts": {"clean": "<PERSON><PERSON><PERSON> lib", "copyFile2": "cp lib/v2/vue-office-pptx.umd.js lib/v2/index.js", "copyFile3": "cp lib/v3/vue-office-pptx.umd.js lib/v3/index.js", "copyReadme": "cp ../../../help.md README.md", "copyType": "cp index.d.ts lib/index.d.ts", "copyScripts": "mkdir lib/script/ && cp ../../script/postinstall.js lib/script/postinstall.js && cp ../../script/switch-cli.js lib/script/switch-cli.js  && cp ../../script/utils.js lib/script/utils.js", "build:2": "npx vue-demi-switch 2 vue2 && vite build && npm run copyFile2", "build:3": "npx vue-demi-switch 3 vue3 && vite build && npm run copyFile3", "build": "npm run clean && npm run build:2 && npm run build:3 && npm run copyScripts && npm run copyReadme && npm run copyType", "postinstall": "node lib/script/postinstall.js"}, "peerDependencies": {"@vue/composition-api": "^1.7.1", "vue": "^2.0.0 || >=3.0.0", "vue-demi": "^0.14.6"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "**************:501351981/vue-office.git"}, "keywords": ["vue", "docx", "pdf", "ppt", "excel", "docx-preview", "excel-preview", "pdf-preview"], "license": "MIT", "author": "微信: _hit757_", "gitHead": "d20568113bec480f6ca72924f6d0c1e3b0f1fe15", "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org"}}