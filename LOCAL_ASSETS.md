# 本地化资源配置指南

## 概述

为了在局域网环境中正常使用文档预览功能，需要将vue-office等前端资源本地化，避免依赖外部CDN。

## 方案选择

### 方案一：自动下载脚本（推荐）

1. **运行下载脚本**：
```bash
cd /path/to/redmine/plugins/redmine_document_viewer
chmod +x download_assets.sh
./download_assets.sh
```

2. **验证下载结果**：
```bash
ls -lh assets/javascripts/
ls -lh assets/stylesheets/
```

3. **检查文件大小**：
   - vue.global.js 应该 > 1MB
   - vue-office-*.umd.js 应该 > 100KB
   - 如果文件很小，说明下载失败

### 方案二：手动下载

如果自动下载失败，请手动下载以下文件：

#### JavaScript文件（保存到 `assets/javascripts/`）：

1. **Vue.js**：
   - URL: https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.js
   - 保存为: `vue.global.js`

2. **vue-office-docx**：
   - URL: https://cdn.jsdelivr.net/npm/@vue-office/docx@1.6.3/lib/v3/index.min.js
   - 保存为: `vue-office-docx.min.js`

3. **vue-office-excel**：
   - URL: https://cdn.jsdelivr.net/npm/@vue-office/excel@1.7.14/lib/v2/vue-office-excel.umd.min.js
   - 保存为: `vue-office-excel.umd.min.js`

4. **vue-office-pdf**：
   - URL: https://cdn.jsdelivr.net/npm/@vue-office/pdf@2.0.10/lib/v3/vue-office-pdf.umd.min.js
   - 保存为: `vue-office-pdf.umd.min.js`

5. **vue-office-pptx**：
   - URL: https://cdn.jsdelivr.net/npm/@vue-office/pptx@1.0.1/+esm
   - 保存为: `vue-office-pptx.esm.js`

#### CSS文件（保存到 `assets/stylesheets/`）：

1. **vue-office-docx.min.css**：
   - URL: https://cdn.jsdelivr.net/npm/@vue-office/docx@1.6.3/lib/v3/index.min.css

2. **vue-office-excel.min.css**：
   - URL: https://cdn.jsdelivr.net/npm/@vue-office/excel@1.7.14/lib/v3/index.min.css

### 方案三：使用简化预览（无需下载）

如果无法下载vue-office资源，插件会自动降级到简化预览模式：

- **图片**：直接显示
- **PDF**：浏览器内置预览
- **Office文档**：提示下载，显示配置说明

## 配置步骤

### 1. 检查资源文件

确保以下文件存在且大小正常：

```
plugins/redmine_document_viewer/assets/
├── javascripts/
│   ├── vue.global.js (>1MB)
│   ├── vue-office-docx.min.js (>100KB)
│   ├── vue-office-excel.umd.min.js (>100KB)
│   ├── vue-office-pdf.umd.min.js (>100KB)
│   └── vue-office-pptx.esm.js (>50KB)
└── stylesheets/
    ├── vue-office-docx.min.css
    └── vue-office-excel.min.css
```

### 2. 配置插件

1. 进入Redmine管理界面
2. 选择 **管理** → **插件**
3. 找到 **Redmine Document Viewer Plugin**
4. 点击 **配置**
5. 选择预览方法为 **Vue Office**

### 3. 测试预览功能

1. 上传一个Word/Excel/PDF文件到任意问题
2. 点击文件旁边的 **预览** 按钮
3. 确认能正常打开预览页面

## 故障排除

### 问题1：预览页面显示"配置在线预览"提示

**原因**：vue-office文件未正确下载或文件损坏

**解决方案**：
1. 检查文件是否存在：`ls -la assets/javascripts/vue*.js`
2. 检查文件大小：`du -h assets/javascripts/vue*.js`
3. 重新运行下载脚本或手动下载文件

### 问题2：下载脚本执行失败

**原因**：网络连接问题或CDN不可访问

**解决方案**：
1. 检查网络连接
2. 尝试使用代理或VPN
3. 手动下载文件
4. 使用其他镜像源

### 问题3：文件下载后大小很小

**原因**：下载的是错误页面而不是实际文件

**解决方案**：
1. 检查下载的文件内容：`head assets/javascripts/vue.global.js`
2. 如果显示HTML内容，说明下载失败
3. 尝试不同的镜像源或手动下载

### 问题4：预览功能正常但样式异常

**原因**：CSS文件未正确加载

**解决方案**：
1. 检查CSS文件是否存在
2. 清除浏览器缓存
3. 重启Redmine服务器

## 备用镜像源

如果默认的jsdelivr镜像无法访问，可以尝试以下镜像：

### 国内镜像：
- https://cdn.bootcdn.net/
- https://unpkg.com/
- https://cdn.staticfile.org/

### 国外镜像：
- https://unpkg.com/
- https://cdnjs.cloudflare.com/
- https://cdn.skypack.dev/

## 离线部署

对于完全离线的环境：

1. 在有网络的机器上下载所有资源文件
2. 将整个 `assets` 目录打包
3. 在离线环境中解压到插件目录
4. 重启Redmine服务器

## 版本兼容性

- Vue.js: 3.x
- vue-office-docx: 1.6.2+
- vue-office-excel: 1.7.11+
- vue-office-pdf: 2.0.2+

如需使用其他版本，请修改 `download_assets.sh` 中的版本号。
