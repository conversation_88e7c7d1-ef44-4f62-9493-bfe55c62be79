# Redmine Document Viewer Plugin

一个功能强大的Redmine插件，**自动在所有附件链接旁边添加预览按钮**，支持在线预览Word、Excel、PDF、图片等多种格式的文档附件。

## ✨ 核心特性

### 🎯 自动集成
- **无需配置**：安装后自动在所有附件链接旁边添加预览按钮
- **全局生效**：在问题、文档、Wiki等所有有附件的地方都会显示预览按钮
- **智能识别**：只为支持预览的文件格式显示预览按钮

### 📁 支持的文件格式
- **图片**: JPG, JPEG, PNG, GIF, BMP, WebP
- **PDF文档**: PDF
- **Word文档**: DOCX, DOC, RTF
- **Excel表格**: XLSX, XLS, CSV
- **PowerPoint演示**: PPTX, PPT

### 🚀 预览方式
1. **Vue Office** (默认推荐) - 纯前端解决方案，无需额外服务
2. **kkFileView** - 开源文档预览服务，支持更多格式
3. **OnlyOffice** - 完整的办公套件，支持在线编辑

### 💡 主要优势
- 🔧 **即装即用**：安装后立即在所有附件旁显示预览按钮
- 📱 **响应式设计**：支持移动端访问
- 🔒 **权限控制**：遵循Redmine原有的附件访问权限
- ⚙️ **灵活配置**：支持多种预览方式切换
- 🎨 **美观界面**：与Redmine界面风格一致

## 安装方法

### 1. 下载插件
```bash
cd /path/to/redmine/plugins
git clone https://github.com/your-username/redmine_document_viewer.git
```

### 2. 安装依赖
```bash
cd /path/to/redmine
bundle install
```

### 3. 运行数据库迁移
```bash
bundle exec rake redmine:plugins:migrate RAILS_ENV=production
```

### 4. 重启Redmine
```bash
# 重启你的Web服务器 (Apache, Nginx, etc.)
```

## 配置说明

### 基础配置
1. 进入 **管理** → **插件** → **Document Viewer Plugin** → **配置**
2. 选择要启用的文件类型
3. 选择预览方法
4. 设置文件大小限制

### Vue Office配置 (推荐)
- **本地化资源**：运行 `./download_assets.sh` 下载本地文件
- **局域网友好**：无需外网访问，适合内网部署
- **纯前端实现**：性能优秀，响应速度快
- **支持格式全面**：Word、Excel、PDF等主流格式

**重要**：为了在局域网环境中正常使用，请先运行资源下载脚本：
```bash
cd plugins/redmine_document_viewer
chmod +x download_assets.sh
./download_assets.sh
```

### kkFileView配置
1. 部署kkFileView服务:
```bash
# 下载kkFileView
wget https://github.com/kekingcn/kkFileView/releases/download/v4.1.0/kkFileView-4.1.0.tar.gz
tar -xzf kkFileView-4.1.0.tar.gz
cd kkFileView-4.1.0/bin
./startup.sh
```

2. 在插件配置中设置kkFileView服务地址: `http://localhost:8012`

### OnlyOffice配置
1. 部署OnlyOffice Document Server:
```bash
# 使用Docker部署
docker run -i -t -d -p 8080:80 onlyoffice/documentserver
```

2. 在插件配置中设置OnlyOffice服务地址: `http://localhost:8080`

## 🎉 使用方法

### 自动显示预览按钮
安装插件后，您会在以下位置**自动看到蓝色的"预览"按钮**：

1. **问题页面** - 在问题附件列表中
2. **文档页面** - 在项目文档的附件中
3. **Wiki页面** - 在Wiki页面的附件中
4. **其他位置** - 任何显示附件的地方

### 预览文档
1. 找到任何支持预览的附件
2. 点击附件链接旁边的蓝色 **"预览"** 按钮
3. 文档将在新窗口中打开进行预览
4. 支持下载原始文件

### 权限说明
- 预览功能遵循Redmine原有的附件访问权限
- 用户只能预览有权限查看的附件
- 无需额外的权限配置

## 开发指南

### 目录结构
```
redmine_document_viewer/
├── app/
│   ├── controllers/
│   │   └── document_viewer_controller.rb
│   ├── helpers/
│   │   └── document_viewer_helper.rb
│   └── views/
│       ├── document_viewer/
│       └── settings/
├── assets/
│   └── stylesheets/
│       └── document_viewer.css
├── config/
│   └── routes.rb
├── init.rb
└── README.md
```

### 扩展新的预览方法
1. 在 `DocumentViewerHelper` 中添加新的URL生成方法
2. 在 `DocumentViewerController` 中添加对应的处理逻辑
3. 在设置页面中添加配置选项

### 添加新的文件格式支持
1. 在 `DocumentViewerHelper#get_file_type` 中添加新的文件类型判断
2. 在 `DocumentViewerController#supported_file_extensions_regex` 中添加新的扩展名
3. 更新相关的图标和样式

## 故障排除

### 常见问题

**Q: 预览页面显示空白**
A: 检查浏览器控制台是否有JavaScript错误，确认vue-office库是否正确加载

**Q: kkFileView预览失败**
A:
- 确认kkFileView服务是否正常运行
- 检查网络连接和防火墙设置
- 确认文件URL是否可以被kkFileView服务访问

**Q: 文件上传后无法预览**
A:
- 检查文件格式是否在支持列表中
- 确认文件大小是否超过限制
- 检查用户是否有相应权限

**Q: 预览速度很慢**
A:
- 启用预览缓存功能
- 考虑使用CDN加速静态资源
- 优化服务器配置

### 日志调试
```bash
# 查看Redmine日志
tail -f log/production.log

# 查看kkFileView日志
tail -f kkFileView/log/kkFileView.log
```

## 性能优化

### 前端优化
- 启用浏览器缓存
- 使用CDN加速vue-office等前端库
- 压缩CSS和JavaScript文件

### 后端优化
- 启用预览缓存
- 使用Redis缓存预览结果
- 配置适当的文件大小限制

### 服务器优化
- 使用SSD存储
- 增加内存配置
- 优化数据库查询

## 贡献指南

1. Fork本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 支持

- 🐛 [报告Bug](https://github.com/your-username/redmine_document_viewer/issues)
- 💡 [功能建议](https://github.com/your-username/redmine_document_viewer/issues)
- 📖 [文档](https://github.com/your-username/redmine_document_viewer/wiki)

## 更新日志

### v1.0.0
- 初始版本发布
- 支持Vue Office预览方式
- 支持Word、Excel、PDF、图片预览
- 基础的权限控制和配置功能
