# Redmine Document Viewer Plugin

一个功能强大的Redmine插件，支持在线预览Word、Excel、PDF、图片等多种格式的文档附件。

## 功能特性

### 支持的文件格式
- **图片**: JPG, JPEG, PNG, GIF, BMP, WebP
- **PDF文档**: PDF
- **Word文档**: DOCX, DOC, RTF
- **Excel表格**: XLSX, XLS, CSV
- **PowerPoint演示**: PPTX, PPT

### 预览方式
1. **Vue Office** (推荐) - 纯前端解决方案，无需额外服务
2. **kkFileView** - 开源文档预览服务，支持更多格式
3. **OnlyOffice** - 完整的办公套件，支持在线编辑

### 主要功能
- 📱 响应式设计，支持移动端
- 🔍 文件类型筛选
- 📊 文件信息展示（大小、上传时间、所属容器）
- 🔒 权限控制
- ⚙️ 灵活的配置选项
- 💾 预览缓存支持
- 🔐 水印功能

## 安装方法

### 1. 下载插件
```bash
cd /path/to/redmine/plugins
git clone https://github.com/your-username/redmine_document_viewer.git
```

### 2. 安装依赖
```bash
cd /path/to/redmine
bundle install
```

### 3. 运行数据库迁移
```bash
bundle exec rake redmine:plugins:migrate RAILS_ENV=production
```

### 4. 重启Redmine
```bash
# 重启你的Web服务器 (Apache, Nginx, etc.)
```

## 配置说明

### 基础配置
1. 进入 **管理** → **插件** → **Document Viewer Plugin** → **配置**
2. 选择要启用的文件类型
3. 选择预览方法
4. 设置文件大小限制

### Vue Office配置 (推荐)
- 无需额外配置
- 纯前端实现，性能优秀
- 支持大部分常用格式

### kkFileView配置
1. 部署kkFileView服务:
```bash
# 下载kkFileView
wget https://github.com/kekingcn/kkFileView/releases/download/v4.1.0/kkFileView-4.1.0.tar.gz
tar -xzf kkFileView-4.1.0.tar.gz
cd kkFileView-4.1.0/bin
./startup.sh
```

2. 在插件配置中设置kkFileView服务地址: `http://localhost:8012`

### OnlyOffice配置
1. 部署OnlyOffice Document Server:
```bash
# 使用Docker部署
docker run -i -t -d -p 8080:80 onlyoffice/documentserver
```

2. 在插件配置中设置OnlyOffice服务地址: `http://localhost:8080`

## 使用方法

### 查看文档列表
1. 进入项目页面
2. 点击 **文档查看器** 菜单
3. 浏览项目中的所有可预览文档

### 预览文档
1. 在文档列表中点击 **预览** 按钮
2. 文档将在新窗口中打开
3. 支持下载原始文件

### 权限管理
- 用户需要有 `查看文档` 权限才能使用文档查看器
- 管理员可以在 **角色和权限** 中配置相关权限

## 开发指南

### 目录结构
```
redmine_document_viewer/
├── app/
│   ├── controllers/
│   │   └── document_viewer_controller.rb
│   ├── helpers/
│   │   └── document_viewer_helper.rb
│   └── views/
│       ├── document_viewer/
│       └── settings/
├── assets/
│   └── stylesheets/
│       └── document_viewer.css
├── config/
│   └── routes.rb
├── init.rb
└── README.md
```

### 扩展新的预览方法
1. 在 `DocumentViewerHelper` 中添加新的URL生成方法
2. 在 `DocumentViewerController` 中添加对应的处理逻辑
3. 在设置页面中添加配置选项

### 添加新的文件格式支持
1. 在 `DocumentViewerHelper#get_file_type` 中添加新的文件类型判断
2. 在 `DocumentViewerController#supported_file_extensions_regex` 中添加新的扩展名
3. 更新相关的图标和样式

## 故障排除

### 常见问题

**Q: 预览页面显示空白**
A: 检查浏览器控制台是否有JavaScript错误，确认vue-office库是否正确加载

**Q: kkFileView预览失败**
A: 
- 确认kkFileView服务是否正常运行
- 检查网络连接和防火墙设置
- 确认文件URL是否可以被kkFileView服务访问

**Q: 文件上传后无法预览**
A: 
- 检查文件格式是否在支持列表中
- 确认文件大小是否超过限制
- 检查用户是否有相应权限

**Q: 预览速度很慢**
A: 
- 启用预览缓存功能
- 考虑使用CDN加速静态资源
- 优化服务器配置

### 日志调试
```bash
# 查看Redmine日志
tail -f log/production.log

# 查看kkFileView日志
tail -f kkFileView/log/kkFileView.log
```

## 性能优化

### 前端优化
- 启用浏览器缓存
- 使用CDN加速vue-office等前端库
- 压缩CSS和JavaScript文件

### 后端优化
- 启用预览缓存
- 使用Redis缓存预览结果
- 配置适当的文件大小限制

### 服务器优化
- 使用SSD存储
- 增加内存配置
- 优化数据库查询

## 贡献指南

1. Fork本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 支持

- 🐛 [报告Bug](https://github.com/your-username/redmine_document_viewer/issues)
- 💡 [功能建议](https://github.com/your-username/redmine_document_viewer/issues)
- 📖 [文档](https://github.com/your-username/redmine_document_viewer/wiki)

## 更新日志

### v1.0.0
- 初始版本发布
- 支持Vue Office预览方式
- 支持Word、Excel、PDF、图片预览
- 基础的权限控制和配置功能
