# Redmine文档查看器前端项目

基于Vite + Vue3 + vue-office的现代化文档预览解决方案。

## 🎯 项目特点

- **现代化技术栈**: Vue3 + Vite + vue-office
- **自动化构建**: 一键构建并复制到Redmine插件assets目录
- **智能预览**: 自动识别文件类型并添加预览按钮
- **响应式设计**: 支持桌面端和移动端
- **错误处理**: 完善的错误处理和降级机制

## 📁 项目结构

```
web/
├── src/
│   ├── components/          # Vue组件
│   │   ├── DocumentViewer.vue      # 主文档查看器组件
│   │   ├── PreviewApp.vue          # 预览应用主组件
│   │   ├── PreviewHeader.vue       # 预览头部组件
│   │   └── previews/               # 各种格式预览组件
│   │       ├── WordPreview.vue     # Word文档预览
│   │       ├── ExcelPreview.vue    # Excel表格预览
│   │       ├── PdfPreview.vue      # PDF文档预览
│   │       ├── ImagePreview.vue    # 图片预览
│   │       ├── PowerPointPreview.vue # PPT预览
│   │       └── DefaultPreview.vue  # 默认预览
│   ├── styles/              # 样式文件
│   │   ├── variables.scss   # SCSS变量
│   │   ├── main.scss        # 主样式（用于Redmine页面）
│   │   └── preview.scss     # 预览页面样式
│   ├── main.js              # 主入口（Redmine页面脚本）
│   └── preview.js           # 预览页面入口
├── scripts/
│   └── copy-assets.js       # 资源复制脚本
├── package.json             # 项目配置
├── vite.config.js           # Vite配置
└── index.html               # 开发预览页面
```

## 🚀 快速开始

### 1. 安装依赖

```bash
cd web
npm install
```

### 2. 开发模式

```bash
# 启动开发服务器
npm run dev

# 在浏览器中打开 http://localhost:3000
```

### 3. 构建生产版本

```bash
# 构建项目
npm run build

# 自动复制到assets目录
npm run copy-assets
```

### 4. 监听模式（推荐开发时使用）

```bash
# 监听文件变化，自动构建和复制
npm run build:watch
```

## 📦 构建输出

构建完成后，会在 `../assets` 目录生成以下文件：

```
assets/
├── javascripts/
│   ├── document-viewer.js   # 主脚本（用于Redmine页面）
│   └── preview-viewer.js    # 预览页面脚本
└── stylesheets/
    ├── document-viewer.css  # 主样式
    └── preview-viewer.css   # 预览页面样式
```

## 🔧 配置说明

### Vite配置 (vite.config.js)

- **多入口配置**: 支持主脚本和预览脚本分别打包
- **资源分类**: 自动将JS、CSS、图片分类到对应目录
- **代码优化**: 生产环境自动压缩和优化
- **路径别名**: 支持@、@components等路径别名

### 构建脚本 (scripts/copy-assets.js)

- **自动复制**: 构建完成后自动复制到Redmine插件目录
- **文件监听**: 支持监听模式，文件变化时自动重新构建
- **统计信息**: 显示复制的文件数量和大小
- **错误处理**: 完善的错误处理和日志输出

## 🎨 样式系统

### SCSS变量 (styles/variables.scss)

定义了完整的设计系统变量：
- 颜色系统（主色、辅助色、状态色）
- 间距系统（xs, sm, md, lg, xl）
- 字体系统（大小、权重、行高）
- 断点系统（响应式设计）

### 主样式 (styles/main.scss)

用于Redmine页面中的预览按钮样式：
- 预览按钮设计
- 响应式适配
- 深色主题支持
- 高对比度模式支持

### 预览样式 (styles/preview.scss)

用于预览页面的完整样式系统：
- 通用组件样式
- 工具类
- 动画效果
- 可访问性支持

## 🧩 组件说明

### DocumentViewer.vue
主文档查看器组件，主要用于开发时测试。实际功能在main.js中实现。

### PreviewApp.vue
预览应用的主组件，负责：
- 文档信息管理
- 预览组件路由
- 错误处理
- 加载状态管理

### 预览组件 (previews/)
各种文件格式的专用预览组件：
- **WordPreview**: 使用@vue-office/docx预览Word文档
- **ExcelPreview**: 使用@vue-office/excel预览Excel表格
- **PdfPreview**: 使用@vue-office/pdf预览PDF文档
- **ImagePreview**: 原生图片预览，支持缩放和全屏
- **PowerPointPreview**: PPT预览（使用Office Online Viewer）
- **DefaultPreview**: 不支持格式的默认预览

## 🔄 开发工作流

### 1. 开发新功能

```bash
# 启动开发服务器
npm run dev

# 修改代码，浏览器自动刷新
# 在 http://localhost:3000 查看效果
```

### 2. 测试集成

```bash
# 构建并复制到Redmine
npm run build
npm run copy-assets

# 在Redmine中测试功能
```

### 3. 持续开发

```bash
# 使用监听模式，自动构建和复制
npm run build:watch

# 修改代码后自动更新到Redmine
```

## 📱 响应式设计

项目完全支持响应式设计：
- **桌面端**: 完整功能和最佳体验
- **平板端**: 适配中等屏幕尺寸
- **移动端**: 优化触摸操作和小屏显示

## ♿ 可访问性

项目遵循Web可访问性标准：
- **键盘导航**: 支持Tab键导航
- **屏幕阅读器**: 语义化HTML和ARIA标签
- **高对比度**: 支持高对比度模式
- **减少动画**: 支持减少动画偏好设置

## 🐛 调试和故障排除

### 开发环境问题

1. **端口冲突**: 修改vite.config.js中的端口设置
2. **依赖问题**: 删除node_modules重新安装
3. **缓存问题**: 清除浏览器缓存或使用无痕模式

### 构建问题

1. **构建失败**: 检查代码语法错误
2. **复制失败**: 确保有写入assets目录的权限
3. **文件缺失**: 检查vite.config.js中的入口配置

### 集成问题

1. **预览按钮不显示**: 检查JavaScript是否正确加载
2. **样式异常**: 确认CSS文件是否正确复制
3. **功能异常**: 查看浏览器控制台错误信息

## 📈 性能优化

- **代码分割**: 按需加载预览组件
- **资源压缩**: 生产环境自动压缩JS和CSS
- **缓存策略**: 合理的文件命名和缓存头设置
- **懒加载**: 图片和大文件懒加载

## 🔮 未来计划

- [ ] 支持更多文件格式
- [ ] 添加文档标注功能
- [ ] 支持文档搜索
- [ ] 添加打印功能
- [ ] 支持文档比较
- [ ] 添加文档转换功能

## 📄 许可证

MIT License
