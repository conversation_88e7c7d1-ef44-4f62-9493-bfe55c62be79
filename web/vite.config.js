import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  
  // 构建配置
  build: {
    // 输出目录
    outDir: '../assets',
    
    // 清空输出目录
    emptyOutDir: true,
    
    // 生成manifest文件
    manifest: true,
    
    // 代码分割配置
    rollupOptions: {
      input: {
        // 主要入口文件
        'document-viewer': resolve(__dirname, 'src/main.js'),
        'preview-viewer': resolve(__dirname, 'src/preview.js')
      },
      output: {
        // 输出文件命名
        entryFileNames: 'javascripts/[name].js',
        chunkFileNames: 'javascripts/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.')
          const ext = info[info.length - 1]
          if (/\.(css)$/.test(assetInfo.name)) {
            return 'stylesheets/[name].[ext]'
          }
          if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/i.test(assetInfo.name)) {
            return 'images/[name].[ext]'
          }
          return 'assets/[name].[ext]'
        }
      }
    },
    
    // 压缩配置
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    },
    
    // 源码映射
    sourcemap: false
  },
  
  // 开发服务器配置
  server: {
    port: 3000,
    open: true,
    cors: true
  },
  
  // 路径解析
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@styles': resolve(__dirname, 'src/styles')
    }
  },
  
  // CSS配置
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@styles/variables.scss";`
      }
    }
  }
})
