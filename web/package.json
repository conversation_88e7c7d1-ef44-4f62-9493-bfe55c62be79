{"name": "redmine-document-viewer-frontend", "version": "1.0.0", "description": "Redmine文档查看器前端项目", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "build:watch": "vite build --watch", "copy-assets": "node scripts/copy-assets.js", "copy-assets:watch": "node scripts/copy-assets.js --watch", "build:prod": "npm run build && npm run copy-assets", "dev:full": "concurrently \"npm run dev\" \"npm run copy-assets:watch\""}, "dependencies": {"vue": "^3.4.0", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "@vue-office/pdf": "^2.0.10"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.0", "vite": "^5.0.0", "sass": "^1.69.0", "fs-extra": "^11.2.0", "chokidar": "^3.5.3", "concurrently": "^8.2.0"}, "engines": {"node": ">=16.0.0"}}