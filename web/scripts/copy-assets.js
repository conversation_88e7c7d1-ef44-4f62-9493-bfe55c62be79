/**
 * 自动复制构建产物到Redmine插件assets目录的脚本
 */

import fs from 'fs-extra'
import path from 'path'
import chokidar from 'chokidar'

const __dirname = path.dirname(new URL(import.meta.url).pathname)
const webDir = path.resolve(__dirname, '..')
const assetsDir = path.resolve(webDir, '../assets')
const distDir = path.resolve(webDir, 'dist')

// 确保目标目录存在
async function ensureDirectories() {
  await fs.ensureDir(path.join(assetsDir, 'javascripts'))
  await fs.ensureDir(path.join(assetsDir, 'stylesheets'))
  await fs.ensureDir(path.join(assetsDir, 'images'))
}

// 复制文件的函数
async function copyAssets() {
  try {
    console.log('🚀 开始复制资源文件...')
    
    // 确保目录存在
    await ensureDirectories()
    
    // 检查dist目录是否存在
    if (!await fs.pathExists(distDir)) {
      console.log('❌ dist目录不存在，请先运行 npm run build')
      return
    }
    
    // 复制JavaScript文件
    const jsFiles = await fs.readdir(distDir)
    for (const file of jsFiles) {
      if (file.endsWith('.js')) {
        const srcPath = path.join(distDir, file)
        const destPath = path.join(assetsDir, 'javascripts', file)
        await fs.copy(srcPath, destPath)
        console.log(`✅ 复制JS: ${file}`)
      }
    }
    
    // 复制CSS文件
    for (const file of jsFiles) {
      if (file.endsWith('.css')) {
        const srcPath = path.join(distDir, file)
        const destPath = path.join(assetsDir, 'stylesheets', file)
        await fs.copy(srcPath, destPath)
        console.log(`✅ 复制CSS: ${file}`)
      }
    }
    
    // 复制其他资源文件
    const assetsSubDir = path.join(distDir, 'assets')
    if (await fs.pathExists(assetsSubDir)) {
      const assetFiles = await fs.readdir(assetsSubDir)
      for (const file of assetFiles) {
        const srcPath = path.join(assetsSubDir, file)
        const stat = await fs.stat(srcPath)
        
        if (stat.isFile()) {
          let destDir = 'assets'
          
          // 根据文件扩展名决定目标目录
          if (/\.(png|jpe?g|gif|svg|ico|webp)$/i.test(file)) {
            destDir = 'images'
          } else if (/\.css$/i.test(file)) {
            destDir = 'stylesheets'
          } else if (/\.js$/i.test(file)) {
            destDir = 'javascripts'
          }
          
          const destPath = path.join(assetsDir, destDir, file)
          await fs.copy(srcPath, destPath)
          console.log(`✅ 复制资源: ${file} -> ${destDir}/`)
        }
      }
    }
    
    // 复制manifest文件（如果存在）
    const manifestPath = path.join(distDir, 'manifest.json')
    if (await fs.pathExists(manifestPath)) {
      const destManifestPath = path.join(assetsDir, 'manifest.json')
      await fs.copy(manifestPath, destManifestPath)
      console.log('✅ 复制manifest.json')
    }
    
    console.log('🎉 资源文件复制完成!')
    
    // 显示复制的文件统计
    await showCopyStats()
    
  } catch (error) {
    console.error('❌ 复制资源文件失败:', error)
    process.exit(1)
  }
}

// 显示复制统计信息
async function showCopyStats() {
  try {
    const jsDir = path.join(assetsDir, 'javascripts')
    const cssDir = path.join(assetsDir, 'stylesheets')
    const imgDir = path.join(assetsDir, 'images')
    
    const jsFiles = await fs.readdir(jsDir).catch(() => [])
    const cssFiles = await fs.readdir(cssDir).catch(() => [])
    const imgFiles = await fs.readdir(imgDir).catch(() => [])
    
    console.log('\n📊 复制统计:')
    console.log(`   JavaScript文件: ${jsFiles.length}个`)
    console.log(`   CSS文件: ${cssFiles.length}个`)
    console.log(`   图片文件: ${imgFiles.length}个`)
    
    // 显示文件大小
    let totalSize = 0
    
    for (const file of jsFiles) {
      const filePath = path.join(jsDir, file)
      const stat = await fs.stat(filePath).catch(() => ({ size: 0 }))
      totalSize += stat.size
    }
    
    for (const file of cssFiles) {
      const filePath = path.join(cssDir, file)
      const stat = await fs.stat(filePath).catch(() => ({ size: 0 }))
      totalSize += stat.size
    }
    
    console.log(`   总大小: ${formatBytes(totalSize)}`)
    
  } catch (error) {
    console.log('无法获取统计信息')
  }
}

// 格式化字节大小
function formatBytes(bytes) {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 监听文件变化（开发模式）
function watchAssets() {
  console.log('👀 开始监听文件变化...')
  
  const watcher = chokidar.watch(distDir, {
    ignored: /node_modules/,
    persistent: true,
    ignoreInitial: true
  })
  
  watcher.on('change', async (filePath) => {
    console.log(`📝 文件变化: ${path.relative(webDir, filePath)}`)
    await copyAssets()
  })
  
  watcher.on('add', async (filePath) => {
    console.log(`➕ 新增文件: ${path.relative(webDir, filePath)}`)
    await copyAssets()
  })
  
  watcher.on('unlink', async (filePath) => {
    console.log(`🗑️  删除文件: ${path.relative(webDir, filePath)}`)
    // 可以在这里添加删除对应目标文件的逻辑
  })
  
  process.on('SIGINT', () => {
    console.log('\n👋 停止监听')
    watcher.close()
    process.exit(0)
  })
}

// 主函数
async function main() {
  const args = process.argv.slice(2)
  const isWatch = args.includes('--watch') || args.includes('-w')
  
  if (isWatch) {
    // 先复制一次，然后开始监听
    await copyAssets()
    watchAssets()
  } else {
    // 只复制一次
    await copyAssets()
  }
}

// 运行脚本
main().catch(error => {
  console.error('❌ 脚本执行失败:', error)
  process.exit(1)
})
