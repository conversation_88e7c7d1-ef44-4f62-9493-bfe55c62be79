<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Redmine文档查看器 - 开发预览</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 20px;
      background: #f5f5f5;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .header {
      text-align: center;
      margin-bottom: 40px;
      padding-bottom: 20px;
      border-bottom: 2px solid #3498db;
    }
    .header h1 {
      color: #2c3e50;
      margin-bottom: 10px;
    }
    .header p {
      color: #7f8c8d;
      font-size: 16px;
    }
    .demo-section {
      margin: 30px 0;
      padding: 20px;
      border: 1px solid #e9ecef;
      border-radius: 6px;
    }
    .demo-section h3 {
      color: #2c3e50;
      margin-bottom: 15px;
    }
    .demo-attachments {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }
    .demo-attachment {
      display: flex;
      align-items: center;
      padding: 10px;
      background: #f8f9fa;
      border-radius: 4px;
    }
    .attachment-icon {
      width: 24px;
      height: 24px;
      margin-right: 10px;
      color: #6c757d;
    }
    .attachment-info {
      flex: 1;
    }
    .attachment-name {
      font-weight: 500;
      color: #495057;
    }
    .attachment-size {
      font-size: 12px;
      color: #6c757d;
    }
    .features {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      margin: 30px 0;
    }
    .feature-card {
      padding: 20px;
      background: #f8f9fa;
      border-radius: 6px;
      border-left: 4px solid #3498db;
    }
    .feature-card h4 {
      color: #2c3e50;
      margin-bottom: 10px;
    }
    .feature-card ul {
      margin: 0;
      padding-left: 20px;
    }
    .feature-card li {
      color: #495057;
      margin-bottom: 5px;
    }
    .tech-stack {
      background: #e8f5e8;
      border-left-color: #28a745;
    }
    .build-info {
      background: #fff3cd;
      border-left-color: #ffc107;
      text-align: center;
      margin-top: 30px;
    }
    .build-info h4 {
      color: #856404;
    }
    .build-info p {
      color: #856404;
      margin: 10px 0;
    }
    .build-commands {
      background: #2c3e50;
      color: white;
      padding: 15px;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
      font-size: 14px;
      margin: 15px 0;
    }
    .build-commands .command {
      margin: 5px 0;
    }
    .build-commands .comment {
      color: #95a5a6;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🚀 Redmine文档查看器</h1>
      <p>基于Vite + Vue3 + vue-office的现代化文档预览解决方案</p>
    </div>

    <div class="demo-section">
      <h3>📎 模拟附件列表（开发预览）</h3>
      <div class="demo-attachments">
        <div class="demo-attachment">
          <svg class="attachment-icon" viewBox="0 0 24 24" fill="currentColor">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
          </svg>
          <div class="attachment-info">
            <div class="attachment-name">项目需求文档.docx</div>
            <div class="attachment-size">2.3 MB</div>
          </div>
          <!-- 预览按钮会通过JavaScript自动添加到这里 -->
        </div>

        <div class="demo-attachment">
          <svg class="attachment-icon" viewBox="0 0 24 24" fill="currentColor">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
          </svg>
          <div class="attachment-info">
            <div class="attachment-name">数据分析表.xlsx</div>
            <div class="attachment-size">1.8 MB</div>
          </div>
        </div>

        <div class="demo-attachment">
          <svg class="attachment-icon" viewBox="0 0 24 24" fill="currentColor">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
          </svg>
          <div class="attachment-info">
            <div class="attachment-name">技术方案.pdf</div>
            <div class="attachment-size">5.2 MB</div>
          </div>
        </div>

        <div class="demo-attachment">
          <svg class="attachment-icon" viewBox="0 0 24 24" fill="currentColor">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
          </svg>
          <div class="attachment-info">
            <div class="attachment-name">界面截图.png</div>
            <div class="attachment-size">856 KB</div>
          </div>
        </div>
      </div>
    </div>

    <div class="features">
      <div class="feature-card">
        <h4>🎯 核心功能</h4>
        <ul>
          <li>自动识别可预览文件</li>
          <li>智能添加预览按钮</li>
          <li>支持多种文档格式</li>
          <li>响应式设计</li>
          <li>错误处理和降级</li>
        </ul>
      </div>

      <div class="feature-card">
        <h4>📁 支持格式</h4>
        <ul>
          <li>图片: JPG, PNG, GIF, WebP</li>
          <li>PDF文档</li>
          <li>Word: DOC, DOCX, RTF</li>
          <li>Excel: XLS, XLSX, CSV</li>
          <li>PowerPoint: PPT, PPTX</li>
        </ul>
      </div>

      <div class="feature-card tech-stack">
        <h4>🛠️ 技术栈</h4>
        <ul>
          <li>Vue 3 + Composition API</li>
          <li>Vite 构建工具</li>
          <li>vue-office 预览组件</li>
          <li>SCSS 样式预处理</li>
          <li>ES6+ 现代JavaScript</li>
        </ul>
      </div>

      <div class="feature-card">
        <h4>🚀 部署优势</h4>
        <ul>
          <li>自动构建和打包</li>
          <li>资源文件自动复制</li>
          <li>代码分割和优化</li>
          <li>开发热重载</li>
          <li>生产环境优化</li>
        </ul>
      </div>
    </div>

    <div class="build-info">
      <h4>🔧 构建和部署</h4>
      <p>使用以下命令构建和部署项目：</p>
      <div class="build-commands">
        <div class="command"># 安装依赖</div>
        <div class="command">npm install</div>
        <div class="command"></div>
        <div class="command"># 开发模式</div>
        <div class="command">npm run dev</div>
        <div class="command"></div>
        <div class="command"># 构建生产版本</div>
        <div class="command">npm run build</div>
        <div class="command"></div>
        <div class="command"># 自动复制到assets目录</div>
        <div class="command">npm run copy-assets</div>
        <div class="command"></div>
        <div class="command"># 监听模式（开发时使用）</div>
        <div class="command">npm run build:watch</div>
      </div>
      <p>构建完成后，资源文件会自动复制到 <code>../assets</code> 目录</p>
    </div>
  </div>

  <!-- 加载开发版本的脚本 -->
  <script type="module" src="/src/main.js"></script>
</body>
</html>
