/**
 * Redmine文档查看器 - 预览页面入口文件
 * 负责文档预览功能
 */

import { createApp } from 'vue'
import PreviewApp from '@components/PreviewApp.vue'
import '@styles/preview.scss'

// 从URL参数或全局变量获取文档信息
function getDocumentInfo() {
  // 尝试从URL路径获取附件ID
  const pathMatch = window.location.pathname.match(/\/doc_viewer\/(\d+)\/preview/)
  const attachmentId = pathMatch ? pathMatch[1] : null
  
  // 从meta标签或全局变量获取文档信息
  const getMetaContent = (name) => {
    const meta = document.querySelector(`meta[name="${name}"]`)
    return meta ? meta.getAttribute('content') : null
  }
  
  return {
    attachmentId,
    filename: getMetaContent('document-filename') || 'unknown',
    filesize: getMetaContent('document-filesize') || '0',
    contentType: getMetaContent('document-content-type') || '',
    downloadUrl: getMetaContent('document-download-url') || '',
    author: getMetaContent('document-author') || '',
    createdOn: getMetaContent('document-created-on') || '',
    // 从全局变量获取（如果有的话）
    ...window.documentInfo
  }
}

// 创建Vue应用
const app = createApp(PreviewApp, {
  documentInfo: getDocumentInfo()
})

// 挂载应用
app.mount('#preview-app')

// 导出给全局使用
window.PreviewApp = app
