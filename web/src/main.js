/**
 * Redmine文档查看器 - 主入口文件
 * 负责在Redmine页面中添加预览按钮
 */

import { createApp } from 'vue'
import DocumentViewer from '@components/DocumentViewer.vue'
import '@styles/main.scss'

// 支持预览的文件扩展名
const PREVIEWABLE_EXTENSIONS = [
  'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp',  // 图片
  'pdf',                                        // PDF
  'doc', 'docx', 'rtf',                        // Word
  'xls', 'xlsx', 'csv',                        // Excel
  'ppt', 'pptx'                                // PowerPoint
]

class RedmineDocumentViewer {
  constructor() {
    this.init()
  }

  init() {
    // 等待DOM加载完成
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.addPreviewButtons())
    } else {
      this.addPreviewButtons()
    }

    // 监听AJAX请求完成
    this.observeAjaxChanges()
    
    // 监听DOM变化
    this.observeDOMChanges()
  }

  /**
   * 检查文件是否支持预览
   */
  isPreviewable(filename) {
    if (!filename) return false
    const extension = filename.split('.').pop().toLowerCase()
    return PREVIEWABLE_EXTENSIONS.includes(extension)
  }

  /**
   * 从链接中获取附件ID
   */
  getAttachmentId(link) {
    const href = link.getAttribute('href')
    if (!href) return null
    
    // 匹配 /attachments/123/filename 或 /attachments/download/123/filename
    const match = href.match(/\/attachments\/(?:download\/)?(\d+)/)
    return match ? match[1] : null
  }

  /**
   * 创建预览按钮
   */
  createPreviewButton(attachmentId, filename) {
    const button = document.createElement('a')
    button.href = `/doc_viewer/${attachmentId}/preview`
    button.target = '_blank'
    button.className = 'redmine-doc-preview-btn'
    button.title = `预览 ${filename}`
    button.textContent = '预览'
    
    // 添加点击事件
    button.addEventListener('click', (e) => {
      e.preventDefault()
      this.openPreview(attachmentId, filename)
    })
    
    return button
  }

  /**
   * 打开预览窗口
   */
  openPreview(attachmentId, filename) {
    const previewUrl = `/doc_viewer/${attachmentId}/preview`
    const windowFeatures = 'width=1200,height=800,scrollbars=yes,resizable=yes'
    window.open(previewUrl, `preview_${attachmentId}`, windowFeatures)
  }

  /**
   * 智能插入预览按钮
   */
  insertPreviewButton(attachmentLink, previewButton) {
    const parent = attachmentLink.parentNode
    
    // 检查父元素类型，决定插入方式
    if (parent.tagName === 'P') {
      // 在段落中，直接在链接后插入
      const wrapper = document.createElement('span')
      wrapper.className = 'attachment-preview-wrapper'
      wrapper.appendChild(previewButton)
      
      if (attachmentLink.nextSibling) {
        parent.insertBefore(wrapper, attachmentLink.nextSibling)
      } else {
        parent.appendChild(wrapper)
      }
    } else if (parent.tagName === 'TD') {
      // 在表格单元格中，添加到末尾
      const wrapper = document.createElement('span')
      wrapper.className = 'attachment-preview-wrapper'
      wrapper.appendChild(previewButton)
      parent.appendChild(wrapper)
    } else {
      // 其他情况，创建包装元素
      const wrapper = document.createElement('span')
      wrapper.className = 'attachment-preview-wrapper'
      wrapper.appendChild(previewButton)
      
      if (attachmentLink.nextSibling) {
        parent.insertBefore(wrapper, attachmentLink.nextSibling)
      } else {
        parent.appendChild(wrapper)
      }
    }
  }

  /**
   * 为附件链接添加预览按钮
   */
  addPreviewButtons() {
    // 查找所有附件链接
    const attachmentSelectors = [
      'a[href*="/attachments/download/"]',
      'a[href*="/attachments/"][href*="/download"]',
      'a.icon-attachment',
      'p a[href*="/attachments/"]'
    ]
    
    attachmentSelectors.forEach(selector => {
      const attachmentLinks = document.querySelectorAll(selector)
      
      attachmentLinks.forEach(link => {
        // 避免重复添加
        if (link.parentNode.querySelector('.redmine-doc-preview-btn') || 
            link.nextElementSibling?.classList.contains('redmine-doc-preview-btn')) {
          return
        }
        
        const attachmentId = this.getAttachmentId(link)
        if (!attachmentId) return
        
        // 从链接文本或href中获取文件名
        let filename = link.textContent.trim()
        if (!filename || filename.length < 3) {
          const href = link.getAttribute('href')
          const parts = href.split('/')
          filename = parts[parts.length - 1]
          // 如果还是没有文件名，尝试从URL参数中获取
          if (!filename || filename.length < 3) {
            const urlParams = new URLSearchParams(href.split('?')[1] || '')
            filename = urlParams.get('filename') || 'unknown'
          }
        }
        
        // 检查是否支持预览
        if (this.isPreviewable(filename)) {
          const previewButton = this.createPreviewButton(attachmentId, filename)
          this.insertPreviewButton(link, previewButton)
        }
      })
    })
  }

  /**
   * 监听AJAX请求完成
   */
  observeAjaxChanges() {
    if (typeof jQuery !== 'undefined') {
      jQuery(document).ajaxComplete(() => {
        setTimeout(() => this.addPreviewButtons(), 100)
      })
    }
  }

  /**
   * 监听DOM变化
   */
  observeDOMChanges() {
    if (window.MutationObserver && document.body) {
      const observer = new MutationObserver(mutations => {
        let shouldUpdate = false
        
        mutations.forEach(mutation => {
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            for (let i = 0; i < mutation.addedNodes.length; i++) {
              const node = mutation.addedNodes[i]
              if (node.nodeType === Node.ELEMENT_NODE) {
                if (node.querySelector && node.querySelector('a[href*="/attachments/"]')) {
                  shouldUpdate = true
                  break
                }
              }
            }
          }
        })
        
        if (shouldUpdate) {
          setTimeout(() => this.addPreviewButtons(), 100)
        }
      })
      
      // 确保document.body存在后再开始观察
      if (document.body) {
        observer.observe(document.body, {
          childList: true,
          subtree: true
        })
      } else {
        document.addEventListener('DOMContentLoaded', () => {
          if (document.body) {
            observer.observe(document.body, {
              childList: true,
              subtree: true
            })
          }
        })
      }
    }
  }
}

// 初始化文档查看器
new RedmineDocumentViewer()

// 导出给全局使用
window.RedmineDocumentViewer = RedmineDocumentViewer
