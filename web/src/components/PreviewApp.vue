<template>
  <div class="preview-app">
    <!-- 预览头部 -->
    <PreviewHeader 
      :filename="documentInfo.filename"
      :filesize="documentInfo.filesize"
      :author="documentInfo.author"
      :created-on="documentInfo.createdOn"
      :download-url="documentInfo.downloadUrl"
      @close="closePreview"
    />
    
    <!-- 预览内容 -->
    <div class="preview-content">
      <component 
        :is="previewComponent"
        :document-info="documentInfo"
        :file-url="documentInfo.downloadUrl"
        @error="handlePreviewError"
        @loaded="handlePreviewLoaded"
      />
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p>正在加载文档...</p>
    </div>
    
    <!-- 错误状态 -->
    <div v-if="error" class="error-overlay">
      <div class="error-content">
        <h3>❌ 预览失败</h3>
        <p>{{ errorMessage }}</p>
        <div class="error-actions">
          <button @click="retryPreview" class="btn btn-primary">重试</button>
          <a :href="documentInfo.downloadUrl" class="btn btn-secondary">下载文件</a>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import PreviewHeader from './PreviewHeader.vue'
import ImagePreview from './previews/ImagePreview.vue'
import PdfPreview from './previews/PdfPreview.vue'
import WordPreview from './previews/WordPreview.vue'
import ExcelPreview from './previews/ExcelPreview.vue'
import PowerPointPreview from './previews/PowerPointPreview.vue'
import DefaultPreview from './previews/DefaultPreview.vue'

export default {
  name: 'PreviewApp',
  components: {
    PreviewHeader,
    ImagePreview,
    PdfPreview,
    WordPreview,
    ExcelPreview,
    PowerPointPreview,
    DefaultPreview
  },
  props: {
    documentInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      loading: true,
      error: false,
      errorMessage: ''
    }
  },
  computed: {
    fileType() {
      const filename = this.documentInfo.filename || ''
      const extension = filename.split('.').pop().toLowerCase()
      
      if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension)) {
        return 'image'
      } else if (extension === 'pdf') {
        return 'pdf'
      } else if (['doc', 'docx', 'rtf'].includes(extension)) {
        return 'word'
      } else if (['xls', 'xlsx', 'csv'].includes(extension)) {
        return 'excel'
      } else if (['ppt', 'pptx'].includes(extension)) {
        return 'powerpoint'
      } else {
        return 'default'
      }
    },
    previewComponent() {
      const componentMap = {
        image: 'ImagePreview',
        pdf: 'PdfPreview',
        word: 'WordPreview',
        excel: 'ExcelPreview',
        powerpoint: 'PowerPointPreview',
        default: 'DefaultPreview'
      }
      return componentMap[this.fileType] || 'DefaultPreview'
    }
  },
  mounted() {
    // 设置页面标题
    document.title = `文档预览 - ${this.documentInfo.filename}`
    
    // 初始化完成，隐藏加载状态
    setTimeout(() => {
      this.loading = false
    }, 500)
  },
  methods: {
    closePreview() {
      if (window.opener) {
        window.close()
      } else {
        history.back()
      }
    },
    handlePreviewError(error) {
      this.loading = false
      this.error = true
      this.errorMessage = error.message || '文档预览失败'
      console.error('预览错误:', error)
    },
    handlePreviewLoaded() {
      this.loading = false
      this.error = false
    },
    retryPreview() {
      this.error = false
      this.loading = true
      
      // 重新加载页面
      setTimeout(() => {
        window.location.reload()
      }, 100)
    }
  }
}
</script>

<style scoped>
.preview-app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.preview-content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  z-index: 1000;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-content {
  text-align: center;
  padding: 40px;
}

.error-content h3 {
  color: #e74c3c;
  margin-bottom: 20px;
}

.error-content p {
  color: #666;
  margin-bottom: 30px;
}

.error-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  font-size: 14px;
  transition: background-color 0.2s;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background: #7f8c8d;
}
</style>
