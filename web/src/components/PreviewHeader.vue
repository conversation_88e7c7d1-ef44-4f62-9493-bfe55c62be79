<template>
  <div class="preview-header">
    <div class="header-content">
      <div class="file-info">
        <h1 class="filename">{{ filename }}</h1>
        <div class="file-meta">
          <span class="file-size">{{ formatFileSize(filesize) }}</span>
          <span v-if="author" class="file-author">上传者: {{ author }}</span>
          <span v-if="createdOn" class="file-date">{{ formatDate(createdOn) }}</span>
        </div>
      </div>
      
      <div class="header-actions">
        <a 
          v-if="downloadUrl" 
          :href="downloadUrl" 
          class="btn btn-download"
          title="下载文件"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
          </svg>
          下载
        </a>
        
        <button 
          @click="$emit('close')" 
          class="btn btn-close"
          title="关闭预览"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
          </svg>
          关闭
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PreviewHeader',
  props: {
    filename: {
      type: String,
      default: '未知文件'
    },
    filesize: {
      type: [String, Number],
      default: 0
    },
    author: {
      type: String,
      default: ''
    },
    createdOn: {
      type: String,
      default: ''
    },
    downloadUrl: {
      type: String,
      default: ''
    }
  },
  emits: ['close'],
  methods: {
    formatFileSize(size) {
      const bytes = parseInt(size) || 0
      if (bytes === 0) return '0 B'
      
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },
    
    formatDate(dateString) {
      if (!dateString) return ''
      
      try {
        const date = new Date(dateString)
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      } catch (error) {
        return dateString
      }
    }
  }
}
</script>

<style scoped>
.preview-header {
  background: #2c3e50;
  color: white;
  padding: 0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  max-width: 100%;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.filename {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  word-break: break-word;
  line-height: 1.3;
}

.file-meta {
  display: flex;
  gap: 20px;
  font-size: 13px;
  color: #bdc3c7;
  flex-wrap: wrap;
}

.file-meta span {
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
  flex-shrink: 0;
}

.btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.btn-download {
  background: #27ae60;
  color: white;
}

.btn-download:hover {
  background: #229954;
  color: white;
}

.btn-close {
  background: #e74c3c;
  color: white;
}

.btn-close:hover {
  background: #c0392b;
}

.btn svg {
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .file-info {
    text-align: center;
  }
  
  .file-meta {
    justify-content: center;
    gap: 15px;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .filename {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 12px 15px;
  }
  
  .file-meta {
    flex-direction: column;
    gap: 5px;
    align-items: center;
  }
  
  .header-actions {
    flex-direction: column;
  }
  
  .btn {
    justify-content: center;
  }
}
</style>
