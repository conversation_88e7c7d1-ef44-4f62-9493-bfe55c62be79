<template>
  <div class="word-preview">
    <VueOfficeDocx
      v-if="!error"
      :src="fileUrl"
      style="height: 100%;"
      @rendered="handleRendered"
      @error="handleError"
    />
    
    <div v-if="error" class="error-fallback">
      <h3>📄 Word文档预览</h3>
      <p>{{ errorMessage }}</p>
      <div class="fallback-actions">
        <button @click="retryPreview" class="btn btn-primary">重试</button>
        <a :href="fileUrl" class="btn btn-secondary">下载文档</a>
      </div>
    </div>
  </div>
</template>

<script>
import VueOfficeDocx from '@vue-office/docx'
import '@vue-office/docx/lib/index.css'

export default {
  name: 'WordPreview',
  components: {
    VueOfficeDocx
  },
  props: {
    fileUrl: {
      type: String,
      required: true
    },
    documentInfo: {
      type: Object,
      required: true
    }
  },
  emits: ['error', 'loaded'],
  data() {
    return {
      error: false,
      errorMessage: ''
    }
  },
  methods: {
    handleRendered() {
      console.log('Word文档渲染完成')
      this.error = false
      this.$emit('loaded')
    },
    
    handleError(error) {
      console.error('Word文档预览错误:', error)
      this.error = true
      this.errorMessage = error.message || 'Word文档加载失败'
      this.$emit('error', error)
    },
    
    retryPreview() {
      this.error = false
      this.errorMessage = ''
      // 触发重新渲染
      this.$forceUpdate()
    }
  }
}
</script>

<style scoped>
.word-preview {
  height: 100%;
  background: white;
}

.error-fallback {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px;
  text-align: center;
  background: white;
}

.error-fallback h3 {
  color: #e74c3c;
  margin-bottom: 20px;
  font-size: 24px;
}

.error-fallback p {
  color: #666;
  margin-bottom: 30px;
  font-size: 16px;
}

.fallback-actions {
  display: flex;
  gap: 15px;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  font-size: 16px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background: #7f8c8d;
}
</style>
