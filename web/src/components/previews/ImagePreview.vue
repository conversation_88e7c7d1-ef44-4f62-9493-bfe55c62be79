<template>
  <div class="image-preview">
    <div class="image-container" @click="toggleFullscreen">
      <img 
        :src="fileUrl" 
        :alt="documentInfo.filename"
        class="preview-image"
        :class="{ 'fullscreen': isFullscreen }"
        @load="handleLoaded"
        @error="handleError"
        @wheel="handleWheel"
        :style="imageStyle"
      />
      
      <!-- 图片控制工具栏 -->
      <div class="image-controls" v-if="!error">
        <button @click.stop="zoomOut" class="control-btn" title="缩小">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 13H5v-2h14v2z"/>
          </svg>
        </button>
        
        <span class="zoom-level">{{ Math.round(scale * 100) }}%</span>
        
        <button @click.stop="zoomIn" class="control-btn" title="放大">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
          </svg>
        </button>
        
        <button @click.stop="resetZoom" class="control-btn" title="重置">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
        </button>
        
        <button @click.stop="toggleFullscreen" class="control-btn" title="全屏">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/>
          </svg>
        </button>
      </div>
    </div>
    
    <!-- 错误状态 -->
    <div v-if="error" class="error-fallback">
      <h3>🖼️ 图片预览</h3>
      <p>{{ errorMessage }}</p>
      <div class="fallback-actions">
        <button @click="retryPreview" class="btn btn-primary">重试</button>
        <a :href="fileUrl" class="btn btn-secondary">下载图片</a>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ImagePreview',
  props: {
    fileUrl: {
      type: String,
      required: true
    },
    documentInfo: {
      type: Object,
      required: true
    }
  },
  emits: ['error', 'loaded'],
  data() {
    return {
      error: false,
      errorMessage: '',
      scale: 1,
      isFullscreen: false,
      translateX: 0,
      translateY: 0
    }
  },
  computed: {
    imageStyle() {
      return {
        transform: `scale(${this.scale}) translate(${this.translateX}px, ${this.translateY}px)`,
        cursor: this.scale > 1 ? 'move' : 'zoom-in'
      }
    }
  },
  methods: {
    handleLoaded() {
      console.log('图片加载完成')
      this.error = false
      this.$emit('loaded')
    },
    
    handleError(error) {
      console.error('图片预览错误:', error)
      this.error = true
      this.errorMessage = '图片加载失败'
      this.$emit('error', new Error(this.errorMessage))
    },
    
    retryPreview() {
      this.error = false
      this.errorMessage = ''
      // 重新加载图片
      const img = this.$el.querySelector('.preview-image')
      if (img) {
        img.src = this.fileUrl + '?t=' + Date.now()
      }
    },
    
    zoomIn() {
      this.scale = Math.min(this.scale * 1.2, 5)
    },
    
    zoomOut() {
      this.scale = Math.max(this.scale / 1.2, 0.1)
    },
    
    resetZoom() {
      this.scale = 1
      this.translateX = 0
      this.translateY = 0
    },
    
    toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen
    },
    
    handleWheel(event) {
      event.preventDefault()
      const delta = event.deltaY > 0 ? 0.9 : 1.1
      this.scale = Math.max(0.1, Math.min(5, this.scale * delta))
    }
  }
}
</script>

<style scoped>
.image-preview {
  height: 100%;
  background: #f8f9fa;
  position: relative;
  overflow: hidden;
}

.image-container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: auto;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: transform 0.2s ease;
  user-select: none;
}

.preview-image.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  max-width: none;
  max-height: none;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.9);
}

.image-controls {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 10px;
  background: rgba(0, 0, 0, 0.7);
  padding: 10px 15px;
  border-radius: 25px;
  backdrop-filter: blur(10px);
}

.control-btn {
  background: transparent;
  border: none;
  color: white;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.zoom-level {
  color: white;
  font-size: 14px;
  font-weight: 500;
  min-width: 50px;
  text-align: center;
}

.error-fallback {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px;
  text-align: center;
  background: white;
}

.error-fallback h3 {
  color: #e74c3c;
  margin-bottom: 20px;
  font-size: 24px;
}

.error-fallback p {
  color: #666;
  margin-bottom: 30px;
  font-size: 16px;
}

.fallback-actions {
  display: flex;
  gap: 15px;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  font-size: 16px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background: #7f8c8d;
}
</style>
