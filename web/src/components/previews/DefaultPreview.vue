<template>
  <div class="default-preview">
    <div class="preview-content">
      <div class="file-icon">
        <svg width="80" height="80" viewBox="0 0 24 24" fill="currentColor">
          <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
        </svg>
      </div>
      
      <h3>{{ getFileTypeDisplay() }}</h3>
      <p class="filename">{{ documentInfo.filename }}</p>
      <p class="file-info">
        文件大小: {{ formatFileSize(documentInfo.filesize) }}
      </p>
      
      <div class="preview-message">
        <p>此文件类型暂不支持在线预览</p>
        <p>请下载文件后使用相应软件打开</p>
      </div>
      
      <div class="actions">
        <a :href="fileUrl" class="btn btn-primary">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
          </svg>
          下载文件
        </a>
        
        <button @click="tryExternalViewer" class="btn btn-secondary">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 19H5V5h7V3H5c-1.11 0-2 .9-2 2v14c1.11 0 2 .89 2 2h14c1.1 0 2-.9 2-2v-7h-2v7zM14 3v2h3.59l-9.83 9.83 1.41 1.41L19 6.41V10h2V3h-7z"/>
          </svg>
          在新窗口打开
        </button>
      </div>
      
      <div class="supported-formats">
        <h4>支持预览的格式：</h4>
        <div class="format-list">
          <span class="format-tag">图片 (JPG, PNG, GIF等)</span>
          <span class="format-tag">PDF文档</span>
          <span class="format-tag">Word文档 (DOC, DOCX)</span>
          <span class="format-tag">Excel表格 (XLS, XLSX)</span>
          <span class="format-tag">PowerPoint (PPT, PPTX)</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DefaultPreview',
  props: {
    fileUrl: {
      type: String,
      required: true
    },
    documentInfo: {
      type: Object,
      required: true
    }
  },
  emits: ['error', 'loaded'],
  mounted() {
    // 标记为已加载
    this.$emit('loaded')
  },
  methods: {
    getFileTypeDisplay() {
      const filename = this.documentInfo.filename || ''
      const extension = filename.split('.').pop().toLowerCase()
      
      const typeMap = {
        txt: '文本文档',
        zip: '压缩文件',
        rar: '压缩文件',
        '7z': '压缩文件',
        exe: '可执行文件',
        dmg: '磁盘镜像',
        iso: '光盘镜像',
        mp4: '视频文件',
        avi: '视频文件',
        mp3: '音频文件',
        wav: '音频文件',
        json: 'JSON文件',
        xml: 'XML文件',
        csv: 'CSV文件'
      }
      
      return typeMap[extension] || '未知文件类型'
    },
    
    formatFileSize(size) {
      const bytes = parseInt(size) || 0
      if (bytes === 0) return '0 B'
      
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },
    
    tryExternalViewer() {
      // 在新窗口中直接打开文件
      window.open(this.fileUrl, '_blank')
    }
  }
}
</script>

<style scoped>
.default-preview {
  height: 100%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-content {
  text-align: center;
  padding: 40px;
  max-width: 600px;
}

.file-icon {
  color: #95a5a6;
  margin-bottom: 20px;
}

.preview-content h3 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 24px;
}

.filename {
  color: #34495e;
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 10px;
  word-break: break-word;
}

.file-info {
  color: #7f8c8d;
  margin-bottom: 30px;
}

.preview-message {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #3498db;
  margin-bottom: 30px;
}

.preview-message p {
  margin: 5px 0;
  color: #666;
}

.actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-bottom: 40px;
}

.btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  font-size: 16px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background: #7f8c8d;
}

.supported-formats {
  text-align: left;
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
}

.supported-formats h4 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 16px;
}

.format-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.format-tag {
  background: #e9ecef;
  color: #495057;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

@media (max-width: 768px) {
  .actions {
    flex-direction: column;
    align-items: center;
  }
  
  .btn {
    width: 200px;
    justify-content: center;
  }
  
  .format-list {
    justify-content: center;
  }
}
</style>
