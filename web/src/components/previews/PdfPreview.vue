<template>
  <div class="pdf-preview">
    <VueOfficePdf
      v-if="!error"
      :src="fileUrl"
      style="height: 100%;"
      @rendered="handleRendered"
      @error="handleError"
    />
    
    <div v-if="error" class="error-fallback">
      <h3>📄 PDF文档预览</h3>
      <p>{{ errorMessage }}</p>
      <div class="fallback-actions">
        <button @click="retryPreview" class="btn btn-primary">重试</button>
        <button @click="tryBrowserPreview" class="btn btn-info">浏览器预览</button>
        <a :href="fileUrl" class="btn btn-secondary">下载PDF</a>
      </div>
      
      <!-- 浏览器内置PDF预览 -->
      <iframe 
        v-if="showBrowserPreview"
        :src="fileUrl"
        class="browser-pdf-viewer"
        title="PDF预览"
      ></iframe>
    </div>
  </div>
</template>

<script>
import VueOfficePdf from '@vue-office/pdf'

export default {
  name: 'PdfPreview',
  components: {
    VueOfficePdf
  },
  props: {
    fileUrl: {
      type: String,
      required: true
    },
    documentInfo: {
      type: Object,
      required: true
    }
  },
  emits: ['error', 'loaded'],
  data() {
    return {
      error: false,
      errorMessage: '',
      showBrowserPreview: false
    }
  },
  methods: {
    handleRendered() {
      console.log('PDF文档渲染完成')
      this.error = false
      this.$emit('loaded')
    },
    
    handleError(error) {
      console.error('PDF文档预览错误:', error)
      this.error = true
      this.errorMessage = error.message || 'PDF文档加载失败'
      this.$emit('error', error)
    },
    
    retryPreview() {
      this.error = false
      this.errorMessage = ''
      this.showBrowserPreview = false
      // 触发重新渲染
      this.$forceUpdate()
    },
    
    tryBrowserPreview() {
      this.showBrowserPreview = true
    }
  }
}
</script>

<style scoped>
.pdf-preview {
  height: 100%;
  background: white;
}

.error-fallback {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px;
  text-align: center;
  background: white;
  position: relative;
}

.error-fallback h3 {
  color: #e74c3c;
  margin-bottom: 20px;
  font-size: 24px;
}

.error-fallback p {
  color: #666;
  margin-bottom: 30px;
  font-size: 16px;
}

.fallback-actions {
  display: flex;
  gap: 15px;
  margin-bottom: 30px;
  flex-wrap: wrap;
  justify-content: center;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  font-size: 16px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-info:hover {
  background: #138496;
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background: #7f8c8d;
}

.browser-pdf-viewer {
  width: 100%;
  height: 500px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

@media (max-width: 768px) {
  .fallback-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .btn {
    width: 200px;
  }
}
</style>
