<template>
  <div class="powerpoint-preview">
    <!-- 暂时使用备用预览，因为vue-office的pptx组件还在开发中 -->
    <div class="fallback-preview">
      <h3>📊 PowerPoint演示文稿</h3>
      <p>正在尝试使用Microsoft Office Online Viewer预览...</p>
      
      <iframe 
        v-if="showOnlineViewer"
        :src="onlineViewerUrl"
        class="office-viewer"
        @load="handleViewerLoaded"
        @error="handleViewerError"
      ></iframe>
      
      <div v-if="viewerError" class="viewer-error">
        <p>在线预览不可用，请下载文件查看</p>
        <div class="fallback-actions">
          <a :href="fileUrl" class="btn btn-primary">下载演示文稿</a>
          <button @click="retryOnlineViewer" class="btn btn-secondary">重试在线预览</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PowerPointPreview',
  props: {
    fileUrl: {
      type: String,
      required: true
    },
    documentInfo: {
      type: Object,
      required: true
    }
  },
  emits: ['error', 'loaded'],
  data() {
    return {
      showOnlineViewer: true,
      viewerError: false,
      viewerLoaded: false
    }
  },
  computed: {
    onlineViewerUrl() {
      // 使用Microsoft Office Online Viewer
      const encodedUrl = encodeURIComponent(window.location.origin + this.fileUrl)
      return `https://view.officeapps.live.com/op/embed.aspx?src=${encodedUrl}`
    }
  },
  mounted() {
    // 设置超时，如果在线预览器加载失败
    setTimeout(() => {
      if (!this.viewerLoaded) {
        this.handleViewerError()
      }
    }, 10000) // 10秒超时
  },
  methods: {
    handleViewerLoaded() {
      console.log('PowerPoint在线预览器加载完成')
      this.viewerLoaded = true
      this.viewerError = false
      this.$emit('loaded')
    },
    
    handleViewerError() {
      console.error('PowerPoint在线预览器加载失败')
      this.viewerError = true
      this.showOnlineViewer = false
      this.$emit('error', new Error('PowerPoint在线预览不可用'))
    },
    
    retryOnlineViewer() {
      this.viewerError = false
      this.showOnlineViewer = true
      this.viewerLoaded = false
      
      // 重新设置超时
      setTimeout(() => {
        if (!this.viewerLoaded) {
          this.handleViewerError()
        }
      }, 10000)
    }
  }
}
</script>

<style scoped>
.powerpoint-preview {
  height: 100%;
  background: white;
}

.fallback-preview {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.fallback-preview h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 24px;
}

.fallback-preview p {
  color: #666;
  margin-bottom: 30px;
  font-size: 16px;
}

.office-viewer {
  width: 100%;
  height: 600px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
}

.viewer-error {
  text-align: center;
}

.viewer-error p {
  color: #e74c3c;
  margin-bottom: 20px;
}

.fallback-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  font-size: 16px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background: #7f8c8d;
}

@media (max-width: 768px) {
  .office-viewer {
    height: 400px;
  }
  
  .fallback-actions {
    flex-direction: column;
    align-items: center;
  }
}
</style>
