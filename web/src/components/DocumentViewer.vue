<template>
  <div class="document-viewer">
    <!-- 这个组件主要用于开发时测试，实际功能在main.js中实现 -->
    <div class="viewer-info">
      <h3>Redmine文档查看器</h3>
      <p>此组件会自动在Redmine页面中为支持的文档添加预览按钮</p>
      
      <div class="supported-formats">
        <h4>支持的文件格式：</h4>
        <ul>
          <li><strong>图片：</strong>JPG, JPEG, PNG, GIF, BMP, WebP</li>
          <li><strong>PDF：</strong>PDF</li>
          <li><strong>Word：</strong>DOC, DOCX, RTF</li>
          <li><strong>Excel：</strong>XLS, XLSX, CSV</li>
          <li><strong>PowerPoint：</strong>PPT, PPTX</li>
        </ul>
      </div>
      
      <div class="demo-buttons">
        <button 
          v-for="format in demoFormats" 
          :key="format.ext"
          class="demo-preview-btn"
          @click="openDemoPreview(format)"
        >
          预览 {{ format.name }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DocumentViewer',
  data() {
    return {
      demoFormats: [
        { ext: 'docx', name: 'Word文档', type: 'word' },
        { ext: 'xlsx', name: 'Excel表格', type: 'excel' },
        { ext: 'pdf', name: 'PDF文档', type: 'pdf' },
        { ext: 'jpg', name: '图片', type: 'image' }
      ]
    }
  },
  methods: {
    openDemoPreview(format) {
      // 打开演示预览窗口
      const demoUrl = `/demo-preview.html?type=${format.type}&ext=${format.ext}`
      const windowFeatures = 'width=1200,height=800,scrollbars=yes,resizable=yes'
      window.open(demoUrl, `demo_preview_${format.type}`, windowFeatures)
    }
  }
}
</script>

<style scoped>
.document-viewer {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.viewer-info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.viewer-info h3 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.supported-formats {
  margin: 20px 0;
}

.supported-formats h4 {
  color: #34495e;
  margin-bottom: 10px;
}

.supported-formats ul {
  list-style-type: none;
  padding: 0;
}

.supported-formats li {
  padding: 5px 0;
  border-bottom: 1px solid #ecf0f1;
}

.supported-formats li:last-child {
  border-bottom: none;
}

.demo-buttons {
  margin-top: 20px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.demo-preview-btn {
  padding: 8px 16px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.demo-preview-btn:hover {
  background: #2980b9;
}
</style>
