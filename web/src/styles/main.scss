// 主样式文件 - 用于Redmine页面中的预览按钮

// 预览按钮样式
.redmine-doc-preview-btn {
  display: inline-block;
  padding: $preview-btn-padding;
  margin-left: $spacing-sm;
  background: $preview-btn-bg;
  color: $preview-btn-color !important;
  text-decoration: none;
  border-radius: $preview-btn-border-radius;
  font-size: $preview-btn-font-size;
  font-weight: $font-weight-medium;
  transition: $transition-base;
  border: none;
  cursor: pointer;
  vertical-align: middle;
  
  &:hover {
    background: $preview-btn-hover-bg;
    color: $preview-btn-color !important;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: $shadow-sm;
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &:before {
    content: "👁";
    margin-right: 4px;
    font-size: 10px;
  }
}

// 附件预览包装器
.attachment-preview-wrapper {
  display: inline-block;
  margin-left: $spacing-xs;
}

// 在表格中的预览按钮样式调整
table.list {
  .redmine-doc-preview-btn {
    font-size: 10px;
    padding: 2px 8px;
    margin-left: $spacing-xs;
    
    &:before {
      font-size: 8px;
    }
  }
  
  .attachment-preview-wrapper {
    margin-left: $spacing-xs;
  }
}

// 在段落中的预览按钮
p .redmine-doc-preview-btn {
  margin-left: $spacing-sm;
}

// 响应式设计
@media (max-width: $breakpoint-sm) {
  .redmine-doc-preview-btn {
    font-size: 10px;
    padding: 3px 8px;
    
    &:before {
      font-size: 8px;
      margin-right: 2px;
    }
  }
}

// 深色主题适配
@media (prefers-color-scheme: dark) {
  .redmine-doc-preview-btn {
    background: lighten($preview-btn-bg, 10%);
    
    &:hover {
      background: lighten($preview-btn-hover-bg, 10%);
    }
  }
}

// 高对比度模式适配
@media (prefers-contrast: high) {
  .redmine-doc-preview-btn {
    border: 1px solid $preview-btn-color;
    font-weight: $font-weight-bold;
  }
}

// 减少动画模式适配
@media (prefers-reduced-motion: reduce) {
  .redmine-doc-preview-btn {
    transition: none;
    
    &:hover {
      transform: none;
    }
  }
}

// 打印样式
@media print {
  .redmine-doc-preview-btn,
  .attachment-preview-wrapper {
    display: none !important;
  }
}
