// 颜色变量
$primary-color: #3498db;
$primary-hover: #2980b9;
$secondary-color: #95a5a6;
$secondary-hover: #7f8c8d;
$success-color: #27ae60;
$success-hover: #229954;
$danger-color: #e74c3c;
$danger-hover: #c0392b;
$warning-color: #f39c12;
$warning-hover: #e67e22;
$info-color: #17a2b8;
$info-hover: #138496;

// 文本颜色
$text-primary: #2c3e50;
$text-secondary: #34495e;
$text-muted: #7f8c8d;
$text-light: #bdc3c7;

// 背景颜色
$bg-primary: #ffffff;
$bg-secondary: #f8f9fa;
$bg-dark: #2c3e50;
$bg-light: #ecf0f1;

// 边框颜色
$border-color: #e9ecef;
$border-light: #f1f3f4;
$border-dark: #dee2e6;

// 阴影
$shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
$shadow-md: 0 2px 6px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.15);

// 圆角
$border-radius-sm: 2px;
$border-radius: 4px;
$border-radius-lg: 8px;
$border-radius-xl: 12px;

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-xxl: 48px;

// 字体大小
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-base: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-xxl: 24px;

// 字体权重
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 行高
$line-height-tight: 1.2;
$line-height-normal: 1.5;
$line-height-relaxed: 1.75;

// 断点
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;

// Z-index层级
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// 过渡动画
$transition-fast: 0.15s ease;
$transition-base: 0.2s ease;
$transition-slow: 0.3s ease;

// 预览按钮样式变量
$preview-btn-bg: $primary-color;
$preview-btn-color: #ffffff;
$preview-btn-hover-bg: $primary-hover;
$preview-btn-padding: 4px 12px;
$preview-btn-font-size: $font-size-xs;
$preview-btn-border-radius: $border-radius;

// 预览窗口样式变量
$preview-header-bg: $bg-dark;
$preview-header-color: #ffffff;
$preview-header-height: 60px;
$preview-content-bg: $bg-secondary;
