// 预览页面样式

// 全局重置
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background: $bg-secondary;
}

#preview-app {
  height: 100vh;
  overflow: hidden;
}

// 通用按钮样式
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: $spacing-xs;
  padding: $spacing-sm $spacing-md;
  border: none;
  border-radius: $border-radius;
  cursor: pointer;
  text-decoration: none;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  transition: $transition-base;
  white-space: nowrap;
  user-select: none;
  
  &:focus {
    outline: 2px solid $primary-color;
    outline-offset: 2px;
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  // 按钮变体
  &.btn-primary {
    background: $primary-color;
    color: white;
    
    &:hover:not(:disabled) {
      background: $primary-hover;
    }
  }
  
  &.btn-secondary {
    background: $secondary-color;
    color: white;
    
    &:hover:not(:disabled) {
      background: $secondary-hover;
    }
  }
  
  &.btn-success {
    background: $success-color;
    color: white;
    
    &:hover:not(:disabled) {
      background: $success-hover;
    }
  }
  
  &.btn-danger {
    background: $danger-color;
    color: white;
    
    &:hover:not(:disabled) {
      background: $danger-hover;
    }
  }
  
  &.btn-warning {
    background: $warning-color;
    color: white;
    
    &:hover:not(:disabled) {
      background: $warning-hover;
    }
  }
  
  &.btn-info {
    background: $info-color;
    color: white;
    
    &:hover:not(:disabled) {
      background: $info-hover;
    }
  }
  
  // 按钮大小
  &.btn-sm {
    padding: $spacing-xs $spacing-sm;
    font-size: $font-size-xs;
  }
  
  &.btn-lg {
    padding: $spacing-md $spacing-lg;
    font-size: $font-size-lg;
  }
}

// 加载动画
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

// 工具类
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-flex { display: flex; }
.d-inline-flex { display: inline-flex; }
.d-block { display: block; }
.d-inline-block { display: inline-block; }
.d-none { display: none; }

.flex-column { flex-direction: column; }
.flex-row { flex-direction: row; }
.align-items-center { align-items: center; }
.align-items-start { align-items: flex-start; }
.align-items-end { align-items: flex-end; }
.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-around { justify-content: space-around; }

.m-0 { margin: 0; }
.mt-1 { margin-top: $spacing-xs; }
.mt-2 { margin-top: $spacing-sm; }
.mt-3 { margin-top: $spacing-md; }
.mt-4 { margin-top: $spacing-lg; }

.mb-1 { margin-bottom: $spacing-xs; }
.mb-2 { margin-bottom: $spacing-sm; }
.mb-3 { margin-bottom: $spacing-md; }
.mb-4 { margin-bottom: $spacing-lg; }

.p-0 { padding: 0; }
.pt-1 { padding-top: $spacing-xs; }
.pt-2 { padding-top: $spacing-sm; }
.pt-3 { padding-top: $spacing-md; }
.pt-4 { padding-top: $spacing-lg; }

.pb-1 { padding-bottom: $spacing-xs; }
.pb-2 { padding-bottom: $spacing-sm; }
.pb-3 { padding-bottom: $spacing-md; }
.pb-4 { padding-bottom: $spacing-lg; }

// 响应式工具类
@media (max-width: $breakpoint-sm) {
  .d-sm-none { display: none; }
  .d-sm-block { display: block; }
  .d-sm-flex { display: flex; }
}

@media (max-width: $breakpoint-md) {
  .d-md-none { display: none; }
  .d-md-block { display: block; }
  .d-md-flex { display: flex; }
}

// 可访问性
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .btn {
    border: 2px solid currentColor;
  }
}

// 打印样式
@media print {
  .btn {
    display: none !important;
  }
  
  .preview-header {
    background: white !important;
    color: black !important;
    border-bottom: 2px solid black;
  }
}
